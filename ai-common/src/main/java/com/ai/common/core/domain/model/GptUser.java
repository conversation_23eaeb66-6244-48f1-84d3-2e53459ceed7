// package com.ai.common.core.domain.model;
//
// import com.ai.common.annotation.Excel;
// import com.ai.common.core.domain.MyBaseEntity;
// import com.baomidou.mybatisplus.annotation.TableName;
// import com.fasterxml.jackson.annotation.JsonFormat;
// import io.swagger.annotations.ApiModel;
// import io.swagger.annotations.ApiModelProperty;
// import lombok.Data;
// import lombok.NoArgsConstructor;
// import lombok.experimental.Accessors;
//
// import java.time.LocalDateTime;
//
// /**
//  * 用户信息对象 gpt_user
//  *
//  * <AUTHOR>
//  * @date 2024-07-25
//  */
// @Data
// @NoArgsConstructor
// @Accessors(chain = true)
// @ApiModel(value = "gpt_user", description = "用户信息")
// @TableName("gpt_user")
// public class GptUser extends MyBaseEntity {
//     private static final long serialVersionUID = 1L;
//
//     /** 用户ID */
//     private Long id;
//
//     /** 部门ID */
//     private Long deptId;
//
//     /** microsoft登录唯一标识 */
//     private String microsoftId;
//
//     /** apple登录唯一标识 */
//     private String appleId;
//
//     /** facebook登录唯一标识 */
//     private String facebookId;
//
//     /** googleId */
//     @ApiModelProperty("googleId")
//     @Excel(name = "googleId")
//     private String googleId;
//
//     /** 微信小程序唯一标识 */
//     private String wechatOpenId;
//
//     /** 登录账号 */
//     @ApiModelProperty("登录账号")
//     @Excel(name = "登录账号")
//     private String loginName;
//
//     /** 用户昵称 */
//     @ApiModelProperty("用户昵称")
//     @Excel(name = "用户昵称")
//     private String userName;
//
//     /** 用户类型（00系统用户 01注册用户） */
//     private String userType;
//
//     /** 角色id */
//     private Long roleId;
//
//     /** 用户邮箱 */
//     @ApiModelProperty("用户邮箱")
//     @Excel(name = "用户邮箱")
//     private String email;
//
//     /** 手机号码 */
//     @ApiModelProperty("手机号码")
//     @Excel(name = "手机号码")
//     private String phoneNumber;
//
//     /** 性别 */
//     @ApiModelProperty("性别")
//     @Excel(name = "性别")
//     private String sex;
//
//     /** 头像 */
//     @ApiModelProperty("头像")
//     @Excel(name = "头像")
//     private String avatar;
//
//     /** 密码 */
//     @ApiModelProperty("密码")
//     @Excel(name = "密码")
//     private String password;
//
//     /** 盐加密 */
//     @ApiModelProperty("盐加密")
//     @Excel(name = "盐加密")
//     private String salt;
//
//     /** 帐号状态（0正常 1停用） */
//     @ApiModelProperty("帐号状态")
//     @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
//     private Integer state;
//
//     /** 删除标志（0代表存在 2代表删除） */
//     private String delFlag;
//
//     /** 最后登录IP */
//     @ApiModelProperty("最后登录IP")
//     @Excel(name = "最后登录IP")
//     private String loginIp;
//
//     /** 最后登录时间 */
//     @ApiModelProperty("最后登录时间")
//     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//     @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
//     private LocalDateTime loginDate;
//
//     /** 密码最后更新时间 */
//     @ApiModelProperty("密码最后更新时间")
//     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//     @Excel(name = "密码最后更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
//     private LocalDateTime pwdUpdateDate;
//
//     /** 用户权限 */
//     private String permission;
//
//     /** 邀请人id */
//     private Long invitedBy;
//
//     /** 账户余额快照值（分） */
//     private Long balance;
//
//     /** 已获得的可用次数 */
//     private Long gainedCount;
//
//     /** 已使用次数 */
//     private Long usedCount;
//
//     /** 用户充值次数 */
//     private Long chargeCount;
//
//     /** 重置密码验证码 */
//     @ApiModelProperty("重置密码验证码")
//     @Excel(name = "重置密码验证码")
//     private String resetPasswordVcode;
//
//     /** gpt上下文记忆条目数 */
//     private Long contextCount;
//
//     /** 最大token数 */
//     private Long contextMaxToken;
//
//     /** 已使用的字数 */
//     private Long usedWordsCount;
//
//     /** 会员有效期起始日期 */
//     private LocalDateTime vipValidStartTime;
//
//     /** 会员有效期结束日期 */
//     private LocalDateTime vipValidEndTime;
//
//     /** 已获得的可用token数 */
//     private Long gainedTokenCount;
//
//     /** 已使用的token数 */
//     private Long usedTokenCount;
//
//     /** 每日会重置的次数记录 */
//     private String dailyCount;
//
//     /** 头像图片名称 */
//     private String avatarName;
//
//     /** 头像图片缩略图名称 */
//     private String thumbnailAvatarName;
//
//     /** 头像图片路径 */
//     private String avatarUrl;
//
//     /** 头像图片缩略图路径 */
//     private String thumbnailAvatarUrl;
//
// }

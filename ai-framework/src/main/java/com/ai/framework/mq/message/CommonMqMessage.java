package com.ai.framework.mq.message;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.rocketmq.shaded.com.google.common.base.Preconditions;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public abstract class CommonMqMessage<T> {

    protected T message;

    protected String messageKey;

    public final String destination() {
        Preconditions.checkNotNull(topic(), "topic should not be null");
        return StrUtil.isEmpty(tag()) ? topic() : topic() + ":" + tag();
    }

    public abstract String topic();

    public abstract String tag();
}

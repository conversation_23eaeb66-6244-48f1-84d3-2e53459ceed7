package com.ai.framework.mq.listener;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseMessageListener implements RocketMQListener {


    public abstract void doWork(MessageView message);

    @Override
    public ConsumeResult consume(MessageView message) {
        log.debug("consumer_message ---current:[{}]----- Receive: {}", this.getClass(), message);
        try {
            doWork(message);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            // 消费失败
            log.error("consume error:", e);
            return ConsumeResult.FAILURE;
        }
    }

    public <T> T getBody(MessageView message, Class<T> clazz) {
        String result = getString(message);
        T t = JSONObject.parseObject(result, clazz);
        return t;
    }

    private static String getString(MessageView message) {
        Charset charset = StandardCharsets.UTF_8;
        ByteBuffer body = message.getBody();
        String result = charset.decode(body)
                .toString();
        body.clear();
        return result;
    }

    public <T> List<T> getListBody(MessageView message, Class<T> clazz) {
        String result = getString(message);
        List<T> tList = null;
        if (result.startsWith("[")) {
            tList = JSONArray.parseArray(result, clazz, JSONReader.Feature.IgnoreAutoTypeNotMatch);
        } else if (result.startsWith("{")) {
            tList = JSONArray.parseArray(result, clazz, JSONReader.Feature.IgnoreAutoTypeNotMatch);
        } else {
            return Collections.EMPTY_LIST;
        }
        return tList;
    }

}

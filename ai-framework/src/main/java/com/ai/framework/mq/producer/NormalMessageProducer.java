package com.ai.framework.mq.producer;

import com.ai.framework.mq.message.CommonMqMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.apache.rocketmq.client.support.RocketMQUtil;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;

/**
 * Description: 消息生产者
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NormalMessageProducer<T> {

    @Resource
    private RocketMQClientTemplate rocketMQClientTemplate;

    /**
     * 同步发送
     *
     * @param commonMqMessage 消息内容
     */
    public void syncSend(CommonMqMessage<T> commonMqMessage) {
        String destination = commonMqMessage.destination();
        Message<T> msg = buildMessage(commonMqMessage);
        rocketMQClientTemplate.send(destination, msg);
        log.info("syncSend to topic: {} msgKey: {}", commonMqMessage.topic(), commonMqMessage.getMessageKey());
    }


    /**
     * 异步发送消息
     *
     * @param commonMqMessage 消息内容
     */
    public void asyncSend(CommonMqMessage<T> commonMqMessage, CompletableFuture<SendReceipt> future0) {
        // springboot不支持使用header传递tags，根据要求，需要在topic后进行拼接 formats: `topicName:tags`，不拼接标识无tag
        String destination = commonMqMessage.destination();
        Message<T> msg = buildMessage(commonMqMessage);
        rocketMQClientTemplate.asyncSendNormalMessage(destination, msg, future0);
    }


    private static <T> Message<T> buildMessage(CommonMqMessage<T> message) {
        String keys = RocketMQUtil.toRocketHeaderKey("KEYS");
        // object消息类型
        Message<T> msg = MessageBuilder.withPayload(message.getMessage())
                .setHeader(keys, message.getMessageKey())
                .build();
        return msg;
    }

    //延时队列
    public void syncDelaySend(CommonMqMessage<T> commonMqMessage, long delayTime) {
        String destination = commonMqMessage.destination();
        Message<T> msg = buildMessage(commonMqMessage);
        rocketMQClientTemplate.syncSendDelayMessage(destination, msg, Duration.ofSeconds(delayTime));
        log.info("delaySend to topic: {} msgKey: {}", commonMqMessage.topic(), commonMqMessage.getMessageKey());
    }


}
//package com.ai.framework.websocket;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.stereotype.Component;
//import org.springframework.web.socket.*;
//import org.springframework.web.socket.handler.TextWebSocketHandler;
//
//import java.io.IOException;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.atomic.AtomicInteger;
//
//@Component
//@Slf4j
//public class WebSocketServer1 extends TextWebSocketHandler {
//
//    /**
//     * 静态变量，用来记录当前在线连接数，线程安全的类。
//     */
//    private static AtomicInteger onlineSessionClientCount = new AtomicInteger(0);
//
//    /**
//     * 存放所有在线的客户端
//     */
//    private static Map<String, WebSocketSession> onlineSessionClientMap = new ConcurrentHashMap<>();
//    private static Map<String, WebSocketSession> comfyUISessionMap = new ConcurrentHashMap<>();
//
//    /**
//     * 群发消息
//     *
//     * @param message 消息
//     */
//    private void sendToAll(String message) {
//        // 遍历在线map集合
//        onlineSessionClientMap.forEach((onlineSid, toSession) -> {
//            // 排除掉自己
//            log.debug("服务端给客户端群发消息 ==> sid = {}, message = {}", onlineSid,
//                    message);
//            try {
//                toSession.sendMessage(new TextMessage(message));
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        });
//    }
//
//    /**
//     * 指定发送消息
//     *
//     * @param toSid
//     * @param message
//     */
//    public synchronized void sendToOne(String toSid, String message) {
//        // 通过sid查询map中是否存在
//        WebSocketSession toSession = onlineSessionClientMap.get(toSid);
//        if (toSession == null) {
//            log.error("服务端给客户端发送消息 ==> toSid = {} 不存在, message = {}", toSid, message);
//            return;
//        }
//        // 异步发送
//        log.debug("服务端给客户端发送消息 ==> toSid = {}, message = {}", toSid, message);
////    toSession.getAsyncRemote().sendText(message);
//
//        // 同步发送
//        try {
//            toSession.sendMessage(new TextMessage(message));
//        } catch (IOException e) {
//            log.error("发送消息失败，WebSocket IO异常");
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 建立连接
//     *
//     * @param session
//     * @throws Exception
//     */
//    @Override
//    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
//        //获取路径sid
//        String sid = (session.getUri().getPath()).split("/")[2];
//        log.debug("connect establishing... ==> session_id = {}， sid = {}", session.getId(), sid);
//        onlineSessionClientMap.put(sid, session);
//
//        //在线数加1
//        onlineSessionClientCount.incrementAndGet();
//        sendToOne(sid, "连接成功");
//        log.debug(
//                "用户websocket连接成功，online count：{} ==> start listen new connect：session_id = {}， sid = {},。",
//                onlineSessionClientCount, session.getId(), sid);
//    }
//
//    /**
//     * 处理前端发来的消息
//     *
//     * @param session
//     * @param message
//     * @throws Exception
//     */
//    @Override
//    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
//        /**
//         * html界面传递来得数据格式，可以自定义.
//         * {"sid":"user-1","message":"hello websocket"}
//         */
//        String toSid = "";
//        String msg = "";
//        if (message.getPayload() instanceof String) {
//            // 处理文本消息
//            TextMessage textMessage = (TextMessage) message;
//            System.out.println("Received text message: " + textMessage.getPayload());
////            JSONObject jsonObject = JSON.parseObject(textMessage.getPayload().toString());
////            toSid = jsonObject.getString("sid");
////            msg = jsonObject.getString("message");
//        }
//        log.debug("服务端收到客户端消息 ==> fromSid = {}, toSid = {}, message = {}", toSid, toSid,
//                message);
//
//        /**
//         * 模拟约定：如果未指定sid信息，则群发，否则就单独发送
//         */
//        if (toSid == null || toSid == "" || "".equalsIgnoreCase(toSid)) {
//            sendToAll(msg);
//        } else {
//            sendToOne(toSid, msg);
//        }
//    }
//
//    @Override
//    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
//        log.error("WebSocket发生错误，错误信息为：" + exception.getMessage());
//        exception.printStackTrace();
//    }
//
//    /**
//     * 断开连接
//     *
//     * @param session
//     * @param closeStatus
//     * @throws Exception
//     */
//    @Override
//    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
//        //获取路径sid
//        String sid = (session.getUri().getPath()).split("/")[2];
//        onlineSessionClientMap.remove(sid);
//        //在线数减1
//        onlineSessionClientCount.decrementAndGet();
//        log.debug(
//                "用户websocket关闭成功，online count：{} ==> close connection info：session_id = {}， sid = {},。",
//                onlineSessionClientCount, session.getId(), sid);
//    }
//
//    @Override
//    public boolean supportsPartialMessages() {
//        return false;
//    }
//
//    // 心跳处理
//    @Override
//    protected void handlePongMessage(WebSocketSession session, PongMessage message) throws Exception {
//        log.debug("Received PONG message from session: {}", session.getId());
//        // 可以在此处处理收到心跳响应的逻辑
//    }
//}

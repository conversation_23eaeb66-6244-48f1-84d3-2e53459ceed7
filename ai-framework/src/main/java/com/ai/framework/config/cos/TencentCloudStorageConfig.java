package com.ai.framework.config.cos;

import lombok.Data;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "tencent-cloud.storage")
@Data
public class TencentCloudStorageConfig {

    private String secretId;
    private String secretKey;
    private String region;
    private String bucketName;
    private String bucketNameOld;
    private String baseSuffix;
    private String accelerateSuffix;
    private String cosDomain;
    /**
     * 全球加速域名
     */
    @Getter
    private String cosAccelerateDomain;

}
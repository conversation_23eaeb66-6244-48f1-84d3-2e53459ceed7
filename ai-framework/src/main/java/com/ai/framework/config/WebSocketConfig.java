package com.ai.framework.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    // 10MB
    private static final int MAX_MESSAGE_SIZE = 10 * 1024 * 1024;
    // 1 hour
    private static final long MAX_IDLE = 60 * 1000;

    /**
     * 设置websocket发送缓存数据大小
     *
     * @return
     */
    @Bean
    public ServletServerContainerFactoryBean createServletServerContainerFactoryBean() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        container.setMaxTextMessageBufferSize(MAX_MESSAGE_SIZE);
        container.setMaxBinaryMessageBufferSize(MAX_MESSAGE_SIZE);
        // 设置默认会话空闲超时 以毫秒为单位 非正值意味着无限超时，默认值 0 ，默认 60s 检查一次空闲就关闭
        container.setMaxSessionIdleTimeout(60 * 1000L);
        // 设置异步发送消息的默认超时时间 以毫秒为单位 非正值意味着无限超时 ，默认值-1，还没看到作用
        container.setAsyncSendTimeout(5 * 1000L);
        return container;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
//    registry.addHandler(getWebSocketServer(), "/websocket/{sid}").setAllowedOrigins("*");
//            .addInterceptors(wSUserAuthInterceptor());
    }

//  @Bean
//  public WebSocketServer1 getWebSocketServer() {
//    return new WebSocketServer1();
//  }

//  @Bean
//  public WSUserAuthInterceptor wSUserAuthInterceptor() {
//    return new WSUserAuthInterceptor();
//  }

}

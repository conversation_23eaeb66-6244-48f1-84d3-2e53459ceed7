// package com.ai.framework.web.service;
//
// import com.ai.common.exception.user.UserPasswordNotMatchException;
// import lombok.SneakyThrows;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.security.authentication.AuthenticationProvider;
// import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
// import org.springframework.security.core.Authentication;
// import org.springframework.security.core.AuthenticationException;
// import org.springframework.security.core.GrantedAuthority;
// import org.springframework.security.core.userdetails.UserDetails;
// import org.springframework.stereotype.Component;
//
// import java.util.Collection;
//
// @Component
// public class PiclumenAuthenticationProvider implements AuthenticationProvider {
//
//
//     @Autowired
//     private PiclumenUserDetailsServiceImpl piclumenUserDetailsService;
//
//     @SneakyThrows
//     @Override
//     public Authentication authenticate(Authentication authentication) throws AuthenticationException {
//
//         String userName = authentication.getName();
//         Object password = authentication.getCredentials();
//
//         // 这里构建来判断用户是否存在和密码是否正确
//         UserDetails userInfo = piclumenUserDetailsService.loadUserByUsername(userName);
//         String pwd =  userInfo.getPassword();
//
//         if (!password.toString().equals(pwd)) {
//             throw new UserPasswordNotMatchException();
//         }
//
//         Collection<? extends GrantedAuthority> authorities = userInfo.getAuthorities();
//         // 构建返回的用户登录成功的token
//         return new UsernamePasswordAuthenticationToken(userInfo, userInfo.getPassword(), authorities);
//     }
//
//     @Override
//     public boolean supports(Class<?> authentication) {
//         return true;
//     }
//
// }
//

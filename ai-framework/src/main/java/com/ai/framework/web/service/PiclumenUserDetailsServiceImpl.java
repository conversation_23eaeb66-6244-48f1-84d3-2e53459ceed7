// package com.ai.framework.web.service;
//
// import com.ai.common.core.domain.model.LoginUser;
// import com.ai.common.exception.ServiceException;
// import com.ai.common.utils.MessageUtils;
// import com.ai.common.core.domain.model.GptUser;
// import com.ai.common.core.domain.model.PiclumenLogin;
// import com.ai.system.service.IGptUserService;
// import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.context.annotation.Primary;
// import org.springframework.security.core.userdetails.UserDetails;
// import org.springframework.security.core.userdetails.UserDetailsService;
// import org.springframework.security.core.userdetails.UsernameNotFoundException;
// import org.springframework.stereotype.Component;
// import org.springframework.stereotype.Service;
//
// import java.util.List;
//
// /**
//  * 用户验证处理
//  *
//  * <AUTHOR>
//  */
// @Service
// public class PiclumenUserDetailsServiceImpl implements UserDetailsService {
//     private static final Logger log = LoggerFactory.getLogger(PiclumenUserDetailsServiceImpl.class);
//
//     @Autowired
//     private IGptUserService iGptUserService;
//
//     @Override
//     public UserDetails loadUserByUsername(String account) throws UsernameNotFoundException {
//         LambdaQueryWrapper<GptUser> queryWrapper = new LambdaQueryWrapper<>();
//         queryWrapper.eq(GptUser::getLoginName, account);
//         List<GptUser> user = iGptUserService.list(queryWrapper);
//         if (user.isEmpty()) {
//             log.info("登录用户：{} 不存在.", account);
//             throw new ServiceException(MessageUtils.message("user.not.exists"));
//         }
//         return createLoginUser(user.get(0));
//     }
//
//     public UserDetails createLoginUser(GptUser user) {
//         return new LoginUser(user.getId(), user.getDeptId(), user);
//     }
// }

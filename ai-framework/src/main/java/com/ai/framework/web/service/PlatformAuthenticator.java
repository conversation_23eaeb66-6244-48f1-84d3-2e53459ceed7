package com.ai.framework.web.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class PlatformAuthenticator {


    @Value("#{${client.config: {'piclumen': '0013423203gg'}}}")
    private Map<String, String> clientSecretMap;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    public Boolean authenticate(Long timestamp, String clientCode, String sign) {
        String key = clientSecretMap.get(clientCode);
        if (StrUtil.isEmpty(key)) {
            log.error("code {} 对应的key为空", clientCode);
            return false;
        }
        String keywords = key + clientCode + timestamp + activeProfile;
        if (SecureUtil.md5(keywords).toUpperCase().equals(sign)) {
            return true;
        }
        return false;
    }
}

// package com.ai.framework.web.service;
//
// import com.ai.common.constant.CacheConstants;
// import com.ai.common.constant.Constants;
// import com.ai.common.core.domain.entity.SysUser;
// import com.ai.common.core.domain.model.LoginUser;
// import com.ai.common.core.redis.RedisCache;
// import com.ai.common.exception.user.*;
// import com.ai.common.utils.DateUtils;
// import com.ai.common.utils.MessageUtils;
// import com.ai.common.utils.StringUtils;
// import com.ai.common.utils.ip.IpUtils;
// import com.ai.framework.manager.AsyncManager;
// import com.ai.framework.manager.factory.AsyncFactory;
// import com.ai.system.service.ISysConfigService;
// import com.ai.system.service.ISysUserService;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.security.authentication.AuthenticationManager;
// import org.springframework.security.authentication.BadCredentialsException;
// import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
// import org.springframework.security.core.Authentication;
// import org.springframework.stereotype.Component;
//
// import javax.annotation.Resource;
//
// /**
//  * 登录校验方法
//  *
//  * <AUTHOR>
//  */
// @Component
// public class PiclumenLoginService {
//     @Autowired
//     private TokenService tokenService;
//     @Resource
//     private AuthenticationManager piclumenAuthenticationManager;
//
//     @Autowired
//     private RedisCache redisCache;
//
//     @Autowired
//     private ISysUserService userService;
//
//     @Autowired
//     private ISysConfigService configService;
//
//     /**
//      * 登录验证
//      *
//      * @param username 用户名
//      * @param password 密码
//      * @return 结果
//      */
//     public String piclumenLogin(String username, String password) {
//         // 用户验证
//         Authentication authentication = null;
//         LoginUser loginUser = null;
//         try {
//             // 该方法会去调用PiclumenUserDetailsServiceImpl.loadUserByUsername
//             authentication = piclumenAuthenticationManager
//                     .authenticate(new UsernamePasswordAuthenticationToken(username, password));
//         } catch (Exception e) {
//             if (e instanceof BadCredentialsException) {
//                 AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
//                 throw new UserPasswordNotMatchException();
//             }
//         }
//         AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
//         if(authentication != null){
//             loginUser = (LoginUser) authentication.getPrincipal();
//         }else {
//             throw new UserPasswordNotMatchException();
//         }
//         // 生成token
//         return tokenService.createToken(loginUser);
//     }
//
//     /**
//      * 校验验证码
//      *
//      * @param username 用户名
//      * @param code     验证码
//      * @param uuid     唯一标识
//      * @return 结果
//      */
//     public void validateCaptcha(String username, String code, String uuid) {
//         boolean captchaEnabled = configService.selectCaptchaEnabled();
//         if (captchaEnabled) {
//             String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
//             String captcha = redisCache.getCacheObject(verifyKey);
//             if (captcha == null) {
//                 AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
//                 throw new CaptchaExpireException();
//             }
//             redisCache.deleteObject(verifyKey);
//             if (!code.equalsIgnoreCase(captcha)) {
//                 AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
//                 throw new CaptchaException();
//             }
//         }
//     }
//
//     /**
//      * 登录前置校验
//      *
//      * @param username 用户名
//      * @param password 用户密码
//      */
//     public void loginPreCheck(String username, String password) {
//         // 用户名或密码为空 错误
//         if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
//             AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
//             throw new UserNotExistsException();
//         }
// //        // 密码如果不在指定范围内 错误
// //        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
// //                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
// //            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
// //            throw new UserPasswordNotMatchException();
// //        }
//         // 用户名不在指定范围内 错误
// //        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
// //                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
// //            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
// //            throw new UserPasswordNotMatchException();
// //        }
//         // IP黑名单校验
//         String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
//         if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
//             AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
//             throw new BlackListException();
//         }
//     }
//
//     /**
//      * 记录登录信息
//      *
//      * @param userId 用户ID
//      */
//     public void recordLoginInfo(Long userId) {
//         SysUser sysUser = new SysUser();
//         sysUser.setUserId(userId);
//         sysUser.setLoginIp(IpUtils.getIpAddr());
//         sysUser.setLoginDate(DateUtils.getNowDate());
//         userService.updateUserProfile(sysUser);
//     }
// }

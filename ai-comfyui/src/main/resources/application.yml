# Spring配置
spring:
  profiles:
    active: dev
  # 配置flyway数据版本管理
  flyway:
    enabled: false
    baseline-on-migrate: true
    clean-on-validation-error: false
    sql-migration-prefix: V
    sql-migration-suffixes: .sql
    locations: classpath:db/migration

    # 邮件
  mail:
    # from 要和 username 一致, 否则报错
    from: <EMAIL>
    #    from: <EMAIL>
    # 邮件服务地址
    #    host: smtp.qq.com
    host: smtp.qiye.aliyun.com
    # 用户名
    #    username: <EMAIL>
    username: <EMAIL>
    # 授权码 (设置 - 账户 - POP3/SMTP服务)
    #    password: nvvqefzuwzuxbbgi
    password: 2xle10KnKjJcKxxi
    # QQ邮箱加密端口，不同邮箱的端口不一样
    port: 465
    toUsers:
      - <EMAIL>
    #      - <EMAIL>
    #      - hesen<PERSON>@info.easeus.com.cn
    #      - <EMAIL>
    properties:
      mail:
        smtp:
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
          ssl:
            #            trust: smtp.qq.com
            trust: smtp.qiye.aliyun.com
          # 是否需要用户认证
          auth: true
          starttls:
            # 启用TLS加密
            enable: true
            required: true

# MQ
rocketmq:
  producer:
    endpoints: rmq-5ev9e5zxk.rocketmq.hk.public.tencenttdmq.com:8080
    accessKey: ak5ev9e5zxk282625f72360
    secretKey: sk8a2bfc5d388474a4
    # seconds 秒
    requestTimeout: 10
    maxAttempts: 3
  pushConsumer:
    endpoints: rmq-5ev9e5zxk.rocketmq.hk.public.tencenttdmq.com:8080
    accessKey: ak5ev9e5zxk282625f72360
    secretKey: sk8a2bfc5d388474a4
  piclumen:
    process:
      enabled: false

# COS
tencent-cloud:
  storage:
    secret-id: "AKIDb9XjhRyaVMsBHSbaM1Yt593i9duxnUHd"
    secret-key: "IWxEa8X2TjsvnSKEpJbhZDejmeSL6e9V"
    region: na-siliconvalley


thirdparty:
  api:
    url: http://192.168.5.202:8080/generate
    test-mode: false  # 设置为 true 启用测试模式

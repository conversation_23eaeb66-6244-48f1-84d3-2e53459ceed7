{"9": {"inputs": {"text": "pink hoodie", "clip": ["19", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "10": {"inputs": {"text": "NSFW, watermask", "clip": ["19", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "14": {"inputs": {"samples": ["40", 0], "vae": ["19", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "19": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "33": {"inputs": {"image": "${inputImg}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "35": {"inputs": {"head": "fooocus_inpaint_head.pth", "patch": "inpaint_v26.fooocus.patch"}, "class_type": "INPAINT_LoadFooocusInpaint", "_meta": {"title": "Load Fooocus Inpaint"}}, "36": {"inputs": {"model": ["19", 0], "patch": ["35", 0], "latent": ["44", 2]}, "class_type": "INPAINT_ApplyFooocusInpaint", "_meta": {"title": "Apply Fooocus Inpaint"}}, "40": {"inputs": {"seed": 150208747849532, "steps": 25, "cfg": 4.5, "sampler_name": "dpm_2", "scheduler": "karras", "denoise": 0.9, "model": ["36", 0], "positive": ["44", 0], "negative": ["44", 1], "latent_image": ["44", 3]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "44": {"inputs": {"positive": ["9", 0], "negative": ["10", 0], "vae": ["19", 2], "pixels": ["33", 0], "mask": ["68", 0]}, "class_type": "INPAINT_VAEEncodeInpaintConditioning", "_meta": {"title": "VAE Encode & Inpaint Conditioning"}}, "59": {"inputs": {"filename_prefix": "ComfyUI", "images": ["65", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "65": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["33", 0], "source": ["14", 0], "mask": ["68", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "68": {"inputs": {"image": "${maskImg}", "channel": "red", "upload": "image"}, "class_type": "LoadImageMask", "_meta": {"title": "Load Image (as <PERSON>)"}}}
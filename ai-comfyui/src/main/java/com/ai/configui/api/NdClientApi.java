package com.ai.configui.api;

import com.ai.configui.domain.dto.ComfyR;
import com.ai.configui.domain.dto.NsfwResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import retrofit2.Response;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class NdClientApi implements ApplicationContextAware {
    @Resource
    ObjectMapper mapper;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private Environment env;
    @Resource
    private NdApi ndApi;
    @Value("${nd.model.url}")
    private String ndModelUrl;
    @Value("${nd.nsfw.url}")
    private String nsfwProcessUrl;
    private static ApplicationContext applicationContext;

    /**
     * 检查当前环境是否为生产环境（prod）
     *
     * @return 如果当前环境为 prod 返回 true，否则返回 false
     */
    public static boolean isProdEnvironment() {
        Environment environment = applicationContext.getEnvironment();
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("prod".equalsIgnoreCase(profile)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isTestEnvironment() {
        Environment environment = applicationContext.getEnvironment();
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("test".equalsIgnoreCase(profile) || "dev".equalsIgnoreCase(profile) || "prestg".equalsIgnoreCase(profile)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public String ndPicture(File file) {
        if (isProdEnvironment() || isTestEnvironment()) {
            return ndOld(file);
        }
        log.info("start nd new");
        return ndNew(file);
    }

    private String ndNew(File file) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, String> param = Map.of("image_path", file.getAbsolutePath());
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, httpHeaders);
        // 构建带参数的 URL
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(ndModelUrl);
        String finalUrl = uriBuilder.toUriString();

        try {
            // 发送请求
            ResponseEntity<Map> exchange = restTemplate.exchange(finalUrl, HttpMethod.POST, requestEntity, Map.class);
            Map body = exchange.getBody();
            if (body == null) {
                return "nsfw error";
            }
            if (body.get("is_nsfw") != null) {
                return (boolean) body.get("is_nsfw") ? "nsfw" : null;
            }
            return "nsfw error";
        } catch (Exception e) {
            log.error("nsfw 请求失败: ", e);
            return "nsfw error";
        }
    }

    private String ndOld(File file) {
        // 准备文件
        RequestBody requestFile = RequestBody.create(okhttp3.MediaType.parse("multipart/form-data"), file);
        MultipartBody.Part filePart = MultipartBody.Part.createFormData("files", file.getName(), requestFile);

        // 鉴黄失败，则当做黄图处理
        try {
            Response<ComfyR<List<NsfwResult>>> response = ndApi.ndPicture(filePart);

            // 获取响应数据
            ComfyR<List<NsfwResult>> result = response.body();
            log.info("鉴黄结果为: {} ，图片名称为：{}", mapper.writeValueAsString(result), file.getName());
            if (0 != result.getStatus() || CollectionUtils.isEmpty(result.getData())) {
                log.error("图片鉴黄失败，图片名称: {}", file.getName());
                return "nsfwError";
            }

            List<NsfwResult> nsfwResultList = result.getData();
            return getNSFWFlag(nsfwResultList.get(0));
        } catch (Exception e) {
            log.error("图片鉴黄异常，图片名称: {}", file.getName(), e);
            return "nsfwError";
        }
    }

    @NotNull
    private static String getNSFWFlag(NsfwResult nsfwResult) {
        String nsfwFlag = "";
        if (nsfwResult.getFemaleBreastExposed() > 0.5) {
            // 女性乳房暴露
            nsfwFlag += "[女性乳房暴露]";
        }
        if (nsfwResult.getFemaleGenitaliaExposed() > 0.5) {
            // 女性生殖器暴露
            nsfwFlag += "[女性生殖器暴露]";
        }
        if (nsfwResult.getAnusExposed() > 0.5) {
            // 肛门暴露
            nsfwFlag += "[肛门暴露]";
        }
        if (nsfwResult.getMaleGenitaliaExposed() > 0.5) {
            // 男性生殖器暴露
            nsfwFlag += "[男性生殖器暴露]";
        }

        return nsfwFlag;
    }

    public Integer nsfwProcess(File file) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            Map<String, String> param = Map.of("image_url", file.getAbsolutePath());
            HttpEntity<Object> requestEntity = new HttpEntity<>(param, httpHeaders);
            // 构建带参数的 URL
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(nsfwProcessUrl);
            String finalUrl = uriBuilder.toUriString();

            // 发送请求
            ResponseEntity<Map> exchange = restTemplate.exchange(finalUrl, HttpMethod.POST, requestEntity, Map.class);
            Map body = exchange.getBody();
            if (body == null) {
                return null;
            }
            if (body.get("data") != null) {
                return Integer.parseInt(body.get("data").toString());
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("nsfw 请求失败: ", e);
            return null;
        }
    }
}

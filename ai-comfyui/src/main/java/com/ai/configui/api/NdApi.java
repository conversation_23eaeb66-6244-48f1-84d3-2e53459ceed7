package com.ai.configui.api;

import com.ai.configui.domain.dto.NsfwResult;
import com.ai.configui.domain.dto.ComfyR;
import com.ai.configui.handle.NdFallbackFactory;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import okhttp3.MultipartBody;
import retrofit2.Response;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;

import java.util.List;

@RetrofitClient(baseUrl = "${nd.url}", fallbackFactory = NdFallbackFactory.class)
// @Intercept(handler = TokenInterceptor.class)
public interface NdApi {


    /**
     * 图片鉴黄接口
     *
     * @param filePart
     * @return
     */
    @Multipart
    @POST("/api/v1/detect")
    Response<ComfyR<List<NsfwResult>>> ndPicture(@Part MultipartBody.Part filePart);
}
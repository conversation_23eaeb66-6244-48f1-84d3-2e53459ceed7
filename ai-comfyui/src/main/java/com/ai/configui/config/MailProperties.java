package com.ai.configui.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "spring.mail")
public class MailProperties {
   
    private String host;

    private String username;

    private String password;

    private String from;

    private Integer port;

    private List<String> toUsers;

    @Value("${spring.mail.properties.mail.smtp.auth}")
    private Boolean auth;

    @Value("${spring.mail.properties.mail.smtp.starttls.enable}")
    private Boolean starttlsEnable;

    @Value("${spring.mail.properties.mail.smtp.ssl.trust}")
    private String sslTrust;

}

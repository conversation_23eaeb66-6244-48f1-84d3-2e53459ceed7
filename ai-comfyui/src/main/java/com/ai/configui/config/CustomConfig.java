package com.ai.configui.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.configui.config.factory.YamlPropertySourceFactory;
import com.ai.configui.domain.dto.ComfyServerInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "custom.server")
@PropertySource(value = "file:${host.config.path}", factory = YamlPropertySourceFactory.class)
public class CustomConfig implements InitializingBean {

    @Setter
    private List<ComfyServerInfo> serverAddress;

    /**
     * socket 链接的ip
     */
    @Value("${custom.server.host:127.0.0.1}")
    @Setter
    private String host;

    /**
     * 本机的IP
     */
    @Value("${custom.server.ip:}")
    @Setter
    private String ip;

    /**
     * key: host and port
     * value: ComfyServerInfo
     */
    private final Map<String, ComfyServerInfo> hostProtServerMap = new HashMap<>();


    /**
     * 端口与具体topic对应
     * key: host and port
     * value: topic
     */
    @Setter
    private Map<String, String> topicAddressMap = new HashMap<>();

    /**
     * key: host+port
     * value: clientId
     */
    private Map<String, String> clientIdMap = new HashMap<>();


    @Override
    public synchronized void afterPropertiesSet() throws Exception {
        topicAddressMap.clear();
        clientIdMap.clear();
        hostProtServerMap.clear();
        if (CollUtil.isNotEmpty(serverAddress)) {
            for (ComfyServerInfo address : serverAddress) {
                hostProtServerMap.put(address.getAddress() + ":" + address.getPort(), address);
                clientIdMap.put(address.getAddress() + ":" + address.getPort(), address.getClientId());
                topicAddressMap.put(address.getAddress() + ":" + address.getPort(), address.getTopic());
            }
        }
    }


    public synchronized boolean addNew(ComfyServerInfo info) {
        String key = host + ":" + info.getPort();
        if (hostProtServerMap.containsKey(key)) {
            return false;
        }
        info.setAddress(host);
        if (StrUtil.isBlank(ip)) {
            this.ip = info.getServerId().split(":")[0];
        }
        serverAddress.add(info);
        try {
            this.afterPropertiesSet();
        } catch (Exception e) {
            log.error("afterPropertiesSet error", e);
            throw new RuntimeException(e);
        }
        return true;
    }

    public synchronized boolean remove(String port) {
        Iterator<ComfyServerInfo> iterator = serverAddress.iterator();
        while (iterator.hasNext()) {
            ComfyServerInfo next = iterator.next();
            if (next.getPort().equals(port)) {
                iterator.remove();
                break;
            }
        }
        try {
            this.afterPropertiesSet();
        } catch (Exception e) {
            log.error("afterPropertiesSet error", e);
            throw new RuntimeException(e);
        }
        return true;
//        if (!hostAndPorts.contains(address)) {
//            return false;
//        }
//        Iterator<String> iterator = serverAddress.iterator();
//        while (iterator.hasNext()) {
//            String next = iterator.next();
//            if (next.startsWith(address)) {
//                iterator.remove();
//                break;
//            }
//        }
//        try {
//            this.afterPropertiesSet();
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//        return true;
    }

}
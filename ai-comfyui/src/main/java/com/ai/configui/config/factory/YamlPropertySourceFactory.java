package com.ai.configui.config.factory;

import com.ai.configui.domain.dto.ComfyServerInfo;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.core.io.support.PropertySourceFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class YamlPropertySourceFactory implements PropertySourceFactory {

    @Override
    public PropertySource<?> createPropertySource(String name, EncodedResource resource) throws IOException {
        YamlPropertiesFactoryBean yamlPropertiesFactoryBean = new YamlPropertiesFactoryBean();
        yamlPropertiesFactoryBean.setResources(resource.getResource());
        yamlPropertiesFactoryBean.afterPropertiesSet();
        Properties propertiesNew = getProperties(yamlPropertiesFactoryBean);
        return new PropertiesPropertySource(Objects.requireNonNull(resource.getResource().getFilename()), propertiesNew);
    }

    private static Properties getProperties(YamlPropertiesFactoryBean yamlPropertiesFactoryBean) {
        Properties properties = yamlPropertiesFactoryBean.getObject();

        List<ComfyServerInfo> list = new ArrayList<>();
        Properties propertiesNew = new Properties();
        propertiesNew.put("custom.server.serverAddress", list);
        // 用于临时存储 serverAddress 的分段键值
        Map<Integer, ComfyServerInfo> tempMap = new HashMap<>();
        if (properties != null) {
            properties.forEach((key, value) -> {
                String keyStr = key.toString();
                if (keyStr.contains("custom.server.serverAddress[")) {
                    // 提取 index 和 属性名，例如 custom.server.serverAddress[0].address
                    int startIndex = keyStr.indexOf('[') + 1;
                    int endIndex = keyStr.indexOf(']');
                    int index = Integer.parseInt(keyStr.substring(startIndex, endIndex));
                    String attribute = keyStr.substring(keyStr.lastIndexOf('.') + 1);
                    // 初始化 index 对应的 Map
                    tempMap.putIfAbsent(index, new ComfyServerInfo());
                    ComfyServerInfo serverInfo = tempMap.get(index);
                    // 反射调用set方法
                    try {
                        serverInfo.getClass().getDeclaredMethod("set" + attribute.substring(0, 1).toUpperCase() + attribute.substring(1), String.class).invoke(serverInfo, value.toString());
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to set property: " + keyStr, e);
                    }
                } else {
                    propertiesNew.put(key, value);
                }
            });
            // 将临时 Map 转换为 List
            tempMap.keySet().stream().sorted().forEach(index -> list.add(tempMap.get(index)));
        }
        return propertiesNew;
    }
}

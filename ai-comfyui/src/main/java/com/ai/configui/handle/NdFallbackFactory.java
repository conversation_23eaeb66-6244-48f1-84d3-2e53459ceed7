package com.ai.configui.handle;


import com.ai.configui.api.NdApi;
import com.github.lianjiatech.retrofit.spring.boot.degrade.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class NdFallbackFactory implements FallbackFactory<NdApi> {
    @Override
    public NdApi create(Throwable cause) {
        System.out.println("触发熔断了!" + cause.getMessage() + " cause:" + cause);
        return null;
    }
}

package com.ai.configui.client.task;

import com.ai.configui.client.SessionWrapper;
import com.ai.configui.config.CustomConfig;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Iterator;
import java.util.Map;

import static com.ai.configui.client.WebSocketClient.SESSIONS;
import static com.ai.configui.client.task.ScheduledTasks.isTaskReady;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ScheduledCountTask {
//    @Resource
//    private NormalMessageProducer<ComfyR<String>> normalMessageProducer;

    @Value("${rocketmq.piclumen.comfy.tag:}")
    private String tag;
    @Value("${rocketmq.piclumen.comfy.topic:}")
    private String topic;

    public static final String COUNT_PATH = "/home/<USER>/ai_logic/logs/count/";
    @Resource
    private CustomConfig config;


    @Scheduled(cron = "0 0,30 * * * ?")
    @Async("threadPoolTaskExecutor")
    public void scheduleTask() {
        if (!isTaskReady) {
            return;
        }
        Map<String, SessionWrapper> sessions = SESSIONS;
        if (sessions.isEmpty()) {
            return;
        }
        Iterator<Map.Entry<String, SessionWrapper>> iterator = sessions.entrySet()
                .iterator();
        JSONObject jsonNode = new JSONObject();
        jsonNode.put("ip", config.getIp());
        long now = System.currentTimeMillis();
        LocalDateTime nowDate = LocalDateTime.now();
        boolean clear = nowDate.getHour() == 0 && nowDate.getMinute() == 0;
        while (iterator.hasNext()) {
            Map.Entry<String, SessionWrapper> entry = iterator.next();
            SessionWrapper sessionWrapper = entry.getValue();
            JSONObject sub = new JSONObject();
            try {
                LocalDateTime openTime = sessionWrapper.getOpenTime();
                Long timestamp = Timestamp.valueOf(openTime).getTime();

                double value = sessionWrapper.getPromptTotalTime() / 1000D;
                sub.put("idleTime", (now - timestamp) / 1000D - value);
                sub.put("promptCount", sessionWrapper.getPromptCount());
                sub.put("promptTotalTime", value);
                sub.put("avgTime", value / sessionWrapper.getPromptCount());
                jsonNode.put(sessionWrapper.getHostPort(), sub);
                if (clear) {
                    sessionWrapper.setPromptCount(0);
                    sessionWrapper.setPromptTotalTime(0);
                }
            } catch (Exception e) {
                log.error("error occur", e);
            }
        }
        if (clear) {
            String format = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            writeJsonToFile(jsonNode, format);
            clearBefore7DaysFile();
        } else {
            writeJsonToFile(jsonNode, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        // 零点的时候清零
//        if (record) {
//            emailService.sendNotifyMessage(JSON.toJSONString(jsonNode, JSONWriter.Feature.PrettyFormat), "comfyui idle time");
////            normalMessageProducer.syncSend(new RMqMessage<>(topic, tag, jsonNode.toJSONString()));
//        }
    }

    private void clearBefore7DaysFile() {
        String fileName = LocalDateTime.now().minusDays(7).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 创建文件夹路径
        String filePath = COUNT_PATH + fileName + ".json";
        try {
            // 如果文件存在 直接删除
            File file = new File(filePath);
            if (file.exists()) {
                if (file.delete()) {
                    log.info("删除文件成功:{}", filePath);
                }
            }
        } catch (Exception e) {
            log.error("clearBefore7DaysFile error", e);
        }
    }

    private static void writeJsonToFile(JSONObject jsonData, String fileName) {
        try {
            // 创建文件夹路径
            String filePath = COUNT_PATH + fileName + ".json";
            File file = new File(filePath);
            // 自动创建父目录（如果不存在）
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
//                log.warn("创建父目录失败");
            }

            // 写入 JSON 数据到文件
            try (FileWriter writer = new FileWriter(file)) {
                writer.write(JSON.toJSONString(jsonData, JSONWriter.Feature.PrettyFormat));
            }
        } catch (IOException e) {
            log.error("写入文件失败", e);
        }
    }
}

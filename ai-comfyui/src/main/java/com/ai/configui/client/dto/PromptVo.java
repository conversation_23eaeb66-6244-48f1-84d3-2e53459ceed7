package com.ai.configui.client.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class PromptVo implements Serializable {

    private String promptId;

    private String hostAndPort;

    private Long startTimeStamp;

    private String clientId;

    private Long endDateTimeStamp;

    private String status;

    public PromptVo() {
    }

    public PromptVo(String promptId, Long startTimeStamp, String clientId) {
        this.promptId = promptId;
        this.startTimeStamp = startTimeStamp;
        this.clientId = clientId;
    }



}

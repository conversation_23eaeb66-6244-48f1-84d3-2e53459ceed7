package com.ai.configui.client.task;

import com.ai.common.utils.TimeWheelTool;
import com.ai.configui.client.CacheComponent;
import com.ai.configui.client.SessionWrapper;
import com.ai.configui.client.handler.CustomSocketHandler;
import com.ai.configui.service.EmailService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.socket.WebSocketHttpHeaders;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import static com.ai.configui.client.WebSocketClient.SESSIONS;

/**
 * 重连任务
 *
 * <AUTHOR>
 */
@Slf4j
public class ReconnectTask implements Runnable {
    @Getter
    private final SessionWrapper sessionWrapper;
    private final StandardWebSocketClient webSocketClient;
    private final CacheComponent cacheComponent;
    private final ObjectMapper objectMapper;
    private final ApplicationEventPublisher eventPublisher;
    private final String hostAndPort;
    private final EmailService emailService;

    public ReconnectTask(SessionWrapper sessionWrapper,
                         String hostAndPort,
                         StandardWebSocketClient webSocketClient,
                         CacheComponent cacheComponent,
                         ObjectMapper objectMapper,
                         ApplicationEventPublisher eventPublisher,
                         EmailService emailService) {
        this.sessionWrapper = sessionWrapper;
        this.hostAndPort = hostAndPort;
        this.webSocketClient = webSocketClient;
        this.cacheComponent = cacheComponent;
        this.objectMapper = objectMapper;
        this.eventPublisher = eventPublisher;
        this.emailService = emailService;
    }

    // 无返回值
    @Override
    public void run() {
        if (sessionWrapper.getSession() != null && sessionWrapper.getSession().isOpen()) {
            log.info("sessionWrapper: {} is open, no need to reconnect", sessionWrapper.getClientId());
            return;
        }
        CustomSocketHandler webSocketHandler = new CustomSocketHandler(hostAndPort, sessionWrapper.getClientId(), cacheComponent, objectMapper, eventPublisher, sessionWrapper);
        String address = sessionWrapper.getHostPort();
        String serverUrl = "ws://" + address + "/ws?clientId=" + sessionWrapper.getClientId();
        ListenableFuture<WebSocketSession> webSocketSessionListenableFuture = null;
        try {
            webSocketSessionListenableFuture = webSocketClient.doHandshake(webSocketHandler, new WebSocketHttpHeaders(), URI.create(serverUrl));
        } catch (Exception e) {
            log.error("WebSocket Client failed to connect to " + serverUrl + " for client_id key: " + hostAndPort, e);
            handleErrorConnect();
            return;
        }

        webSocketSessionListenableFuture.addCallback(sessionNew -> {
            // 重置时间
            sessionWrapper.setOpenTime(LocalDateTime.now());
            sessionWrapper.init();
            sessionWrapper.setSession(sessionNew);
            SESSIONS.put(hostAndPort, sessionWrapper);
            log.info("WebSocket Client connected to " + serverUrl + " for client_id key: " + sessionWrapper.getClientId());
        }, e -> {
            handleErrorConnect();
            log.error("WebSocket Client failed to connect to " + serverUrl + " for client_id key: " + hostAndPort, e);
        });

    }

    private void handleErrorConnect() {
        // 计算下一次 重连时间
        int reconnectCount = sessionWrapper.getReconnectCount();
        if (reconnectCount == 10 || reconnectCount == 20 || reconnectCount == 30 || reconnectCount % 60 == 0) {
            emailService.sendNotifyMessage(
                    String.format("[%s]重连socket失败，请检查配置文件，重连次数：%d, 端口： %s, client_id：%s ", sessionWrapper.getIp(), reconnectCount, hostAndPort,
                            sessionWrapper.getClientId()), "comfy 重连socket失败");
        }
        int intervalSeconds = sessionWrapper.getIntervalSeconds();
        int min = Math.min(intervalSeconds * 2, 60);
        sessionWrapper.setReconnectCount(reconnectCount + 1);
        sessionWrapper.setIntervalSeconds(min);
        ReconnectTask reconnectTask = new ReconnectTask(sessionWrapper, hostAndPort, webSocketClient, cacheComponent, objectMapper, eventPublisher, emailService);
        TimeWheelTool.scheduleTask(hostAndPort, reconnectTask, sessionWrapper.getIntervalSeconds(), TimeUnit.SECONDS);
    }

}
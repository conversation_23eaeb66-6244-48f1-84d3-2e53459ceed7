package com.ai.configui.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SessionWrapper {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime openTime;

    @JsonIgnore
    private long promptCount = 0;

    @JsonIgnore
    private long promptTotalTime = 0;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime closeTime;

    @JsonIgnore
    private AtomicInteger pingCount = new AtomicInteger(0);

    @JsonIgnore
    private AtomicInteger zeroQueueCount = new AtomicInteger(0);

    private CloseStatus closeStatus;

    private int reconnectCount = 1;
    // 间隔秒数
    private int intervalSeconds = 1;

    @JsonIgnore
    private WebSocketSession session;

    private String hostPort;

    private String clientId;

    private String ip;

    public SessionWrapper() {
    }

    public SessionWrapper(WebSocketSession session) {
        this.session = session;
    }

    public SessionWrapper(LocalDateTime openTime, WebSocketSession session) {
        this.openTime = openTime;
        this.session = session;
    }

    public SessionWrapper(String clientId, String address, String ip) {
        this.clientId = clientId;
        this.hostPort = address;
        this.ip = ip;
    }

    public void incrCount() {
        promptCount++;
    }

    public void incrZeroQueueCount() {
        this.zeroQueueCount.incrementAndGet();
    }

    public void clearZeroQueueCount() {
        this.zeroQueueCount.set(0);
    }


    public void incrTotalTime(long time) {
        promptTotalTime += time;
    }

    public SessionWrapper init() {
        this.setCloseTime(null);
        this.setIntervalSeconds(1);
        this.setReconnectCount(1);
        this.setCloseStatus(null);
        this.setPingCount(new AtomicInteger(0));
        return this;

    }
}

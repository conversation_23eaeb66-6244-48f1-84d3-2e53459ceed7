package com.ai.configui.client.task;

import com.ai.common.utils.TimeWheelTool;
import com.ai.configui.client.CacheComponent;
import com.ai.configui.client.SessionWrapper;
import com.ai.configui.service.EmailService;
import com.ai.configui.service.IReadyServerService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.PingMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import javax.annotation.Resource;
import javax.websocket.WebSocketContainer;
import java.io.IOException;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.ai.configui.client.WebSocketClient.SESSIONS;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ScheduledTasks {

    private StandardWebSocketClient standardWebSocketClient;

    private AtomicInteger count = new AtomicInteger(0);
    @Resource
    private CacheComponent cacheComponent;
    @Resource
    private ObjectMapper objectMapper;
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private EmailService emailService;
    @Resource
    private IReadyServerService readyServerService;
    public static volatile boolean isTaskReady = false;

    public ScheduledTasks(WebSocketContainer webSocketContainer) {
        standardWebSocketClient = new StandardWebSocketClient(webSocketContainer);
    }

    @Scheduled(cron = "*/2 * * * * ?")
    @Async("scheduledExecutorService")
    public void scheduleTask() {
        if (!isTaskReady) {
            return;
        }
        Map<String, SessionWrapper> sessions = SESSIONS;
        if (sessions.isEmpty()) {
//            log.debug("No active WebSocket sessions found.");
            return;
        }
        int countNow = count.addAndGet(1);

        Iterator<Map.Entry<String, SessionWrapper>> iterator = sessions.entrySet()
                .iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, SessionWrapper> entry = iterator.next();
            SessionWrapper sessionWrapper = entry.getValue();
            WebSocketSession session = sessionWrapper.getSession();
            String hostAndPort = entry.getKey();
            boolean closeOrNotHasSession = session == null || !session.isOpen();
            if (closeOrNotHasSession) {
                iterator.remove();
                log.info("Session closed, add reconnect task, hostAndPort: {} ", hostAndPort);
                andToReconnectTask(sessionWrapper, hostAndPort);
            } else if (countNow % 5 == 0) {
                count.set(0);
                // 每三十秒一次心跳
                sendPingMessage(sessionWrapper, session, iterator, hostAndPort);
            } else {
                AtomicInteger pingCount = sessionWrapper.getPingCount();
                if (pingCount.get() > 0 && pingCount.get() <= 3) {
                    sendPingMessage(sessionWrapper, session, iterator, hostAndPort);
                } else if (pingCount.get() > 3) {
                    log.info("WebSocket Client ping count not with pong message: {}", pingCount.get());
                    try {
                        iterator.remove();
                        session.close();
                        andToReconnectTask(sessionWrapper, hostAndPort);
                    } catch (IOException e) {
                        log.error("关闭WebSocket连接失败2", e);
                    }
                }
            }
        }
    }

    private void sendPingMessage(SessionWrapper sessionWrapper, WebSocketSession session, Iterator<Map.Entry<String, SessionWrapper>> iterator, String hostAndPort) {
        try {
            sessionWrapper.getPingCount().incrementAndGet();
            session.sendMessage(new PingMessage());
        } catch (Exception e) {
            log.error("发送消息Ping失败，WebSocket IO异常", e);
            try {
                iterator.remove();
                session.close();
                andToReconnectTask(sessionWrapper, hostAndPort);
            } catch (IOException ex) {
                log.error("关闭WebSocket连接失败1", ex);
            }
        }
    }

    private void andToReconnectTask(SessionWrapper sessionWrapper, String hostAndPort) {
        ReconnectTask reconnectTask = new ReconnectTask(sessionWrapper, hostAndPort, standardWebSocketClient, cacheComponent, objectMapper, eventPublisher, emailService);
        TimeWheelTool.scheduleTask(hostAndPort, reconnectTask, sessionWrapper.getIntervalSeconds(), TimeUnit.SECONDS);
    }
}

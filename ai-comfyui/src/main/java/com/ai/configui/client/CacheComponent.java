package com.ai.configui.client;

import com.ai.configui.client.dto.PromptVo;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class CacheComponent {

    public static final int CACHE_TIME = 120;

    // 创建 Caffeine 缓存
    /**
     * key : host and port
     * value:  map
     * map-key: promptId
     * map-value: promptVo
     */
    public Cache<String, Cache<String, PromptVo>> cache = Caffeine.newBuilder()
            .maximumSize(50)
            .build();

}

package com.ai.configui.client.error;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ErrorLogManager {

    // 5MB
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;
    // 保留天数
    private static final int RETENTION_DAYS = 5;
    // 日志存储目录
    private static final String LOG_DIR = "/home/<USER>/ai_logic/logs/error";
    // 日志基础文件名
    private static final String LOG_FILE_NAME = "error_log";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final ThreadPoolTaskExecutor LOG_EXECUTOR;

    static {
        File logDir = new File(LOG_DIR);
        if (!logDir.exists()) {
            logDir.mkdirs();
        }
        LOG_EXECUTOR = new ThreadPoolTaskExecutor();
        LOG_EXECUTOR.setMaxPoolSize(4);
        LOG_EXECUTOR.setCorePoolSize(2);
        LOG_EXECUTOR.setQueueCapacity(10);
        LOG_EXECUTOR.setKeepAliveSeconds(60);
        // 线程池对拒绝任务(无线程可用)的处理策略
        LOG_EXECUTOR.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        LOG_EXECUTOR.initialize();
    }


    /**
     * 添加错误日志（异步写入）
     *
     * @param promptId 唯一标识
     * @param errorMsg 错误信息
     */
    public static void addErrorAsync(String promptId, String errorMsg) {
        if (promptId == null || promptId.isEmpty() || errorMsg == null || errorMsg.isEmpty()) {
            return;
        }

        // 异步写入文件
        try {
            LOG_EXECUTOR.submit(() -> {
                File logFile = getCurrentLogFile();
                try (FileWriter writer = new FileWriter(logFile, true)) {
                    String logEntry = OBJECT_MAPPER.writeValueAsString(Map.of(promptId, errorMsg)) + System.lineSeparator();
                    writer.write(logEntry);
                } catch (IOException e) {
                    log.error("Error writing to log file: ", e);
                }

                // 检查文件大小并分割
                splitLogFileIfNeeded(logFile);

                // 清理过期日志文件
                cleanOldLogs();
            });
        } catch (Exception e) {
            log.error("Error adding error log: ", e);
        }
    }

    /**
     * 查询错误日志
     *
     * @param promptId  唯一标识
     * @param queryDate
     * @return 错误信息
     */
    public static String getError(String promptId, String queryDate) {
        Map<String, String> errorMap = loadLogsFromFiles(queryDate);
        String orDefault = errorMap.getOrDefault(promptId, "No error message found for this promptId.");
        errorMap.clear();
        return orDefault;
    }

    /**
     * 获取当前日志文件
     */
    private static File getCurrentLogFile() {
        String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        return new File(LOG_DIR, LOG_FILE_NAME + "_" + date + ".log");
    }

    /**
     * 检查日志文件大小，并在超过阈值时分割
     */
    private static void splitLogFileIfNeeded(File logFile) {
        if (logFile.length() > MAX_FILE_SIZE) {
            String newFileName = logFile.getName().replace(".log", "") + "_" + System.currentTimeMillis() + ".log";
            File newFile = new File(LOG_DIR, newFileName);
            logFile.renameTo(newFile);
        }
    }

    /**
     * 清理 5 天前的日志文件
     */
    private static void cleanOldLogs() {
        File logDir = new File(LOG_DIR);
        File[] files = logDir.listFiles((dir, name) -> name.startsWith(LOG_FILE_NAME) && name.endsWith(".log"));
        if (files != null) {
            if (files.length < 5) {
                return;
            }
            long now = System.currentTimeMillis();
            for (File file : files) {
                try {
                    BasicFileAttributes attrs = Files.readAttributes(file.toPath(), BasicFileAttributes.class);
                    long creationTime = attrs.creationTime().toMillis();
                    if (now - creationTime > RETENTION_DAYS * 24 * 60 * 60 * 1000L) {
                        if (!file.delete()) {
                            log.error("Failed to delete old log file: " + file.getName());
                        }
                    }
                } catch (IOException e) {
                    log.error("Error reading file attributes: " + e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 从日志文件加载数据到内存缓存
     */
    private static Map<String, String> loadLogsFromFiles(String queryDate) {
        File logDir = new File(LOG_DIR);
        if (queryDate == null) {
            LocalDate now = LocalDate.now();
            queryDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        String finalQueryDate = queryDate;
        File[] files = logDir.listFiles((dir, name) -> name.startsWith(LOG_FILE_NAME) && name.endsWith(".log") && name.contains(finalQueryDate));
        Map<String, String> errorMap = new HashMap<>();
        if (files != null) {
            for (File file : files) {
                try {
                    List<String> lines = Files.readAllLines(file.toPath());
                    for (String line : lines) {
                        Map<String, String> entry = OBJECT_MAPPER.readValue(line, Map.class);
                        errorMap.putAll(entry);
                    }
                } catch (IOException e) {
                    log.error("Error reading log file: " + e.getMessage(), e);
                    throw new RuntimeException("Error reading log file: " + e.getMessage());
                }
            }
        }
        return errorMap;
    }

    /**
     * 关闭异步线程池
     */
    public static void shutdown() {
        LOG_EXECUTOR.shutdown();
        try {
            if (!LOG_EXECUTOR.getThreadPoolExecutor().awaitTermination(5, TimeUnit.SECONDS)) {
                LOG_EXECUTOR.shutdown();
            }
        } catch (InterruptedException e) {
            LOG_EXECUTOR.shutdown();
        }
    }
}

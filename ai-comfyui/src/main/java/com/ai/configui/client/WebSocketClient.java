package com.ai.configui.client;

import cn.hutool.core.thread.ThreadUtil;
import com.ai.common.utils.TimeWheelTool;
import com.ai.configui.client.dto.PromptVo;
import com.ai.configui.client.handler.CustomSocketHandler;
import com.ai.configui.config.CustomConfig;
import com.ai.configui.event.PromptEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.websocket.server.WsServerContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.socket.WebSocketHttpHeaders;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.websocket.ContainerProvider;
import javax.websocket.WebSocketContainer;
import java.io.IOException;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.ai.configui.client.CacheComponent.CACHE_TIME;
import static com.ai.configui.client.task.ScheduledTasks.isTaskReady;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WebSocketClient {

    @Resource
    private CacheComponent cacheComponent;
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private ObjectMapper objectMapper;
    public static final Map<String, SessionWrapper> SESSIONS = new ConcurrentHashMap<>();
    @Autowired
    private CustomConfig config;

    @Autowired
    private WebSocketContainer webSocketContainer;

    @PostConstruct
    public void init() {
        log.info("comfy status monitor init");
        new Thread(() -> {
            isTaskReady = false;
            Map<String, String> clientCacheKeyMap = config.getClientIdMap();
            if (clientCacheKeyMap.isEmpty()) {
                log.warn("no comfy instance found from redis, stop");
            } else {
                clientCacheKeyMap.forEach((hostPort, clientIdStr) -> {
                    initToCache(cacheComponent, eventPublisher, hostPort);
                    connectToWebSocket(hostPort, clientIdStr);
                });
            }
            isTaskReady = true;
        }).start();
    }

    public static Cache<String, PromptVo> initToCache(CacheComponent cacheComponent, ApplicationEventPublisher eventPublisher, String hostPort) {
        // key promptId
        Cache<String, PromptVo> build = Caffeine.newBuilder()
                .scheduler(Scheduler.systemScheduler())
                .maximumSize(20)
                // 四分半
                .expireAfterWrite(CACHE_TIME, TimeUnit.SECONDS)
                .removalListener(new RemovalListener<String, PromptVo>() {
                    @Override
                    public void onRemoval(String key, PromptVo value, RemovalCause cause) {
                        if (cause == RemovalCause.EXPIRED) {
                            log.debug("Key {} with value {} was expired and remove", key, value);
                            PromptEvent promptEvent = new PromptEvent(this, key, hostPort, PromptEvent.EventType.cache_expired);
                            eventPublisher.publishEvent(promptEvent);
                        } else if (cause == RemovalCause.REPLACED) {

                        }
                    }
                })
                .executor(Executors.newFixedThreadPool(1))
                .build();
        cacheComponent.cache.put(hostPort, build);
        return build;
    }

    public boolean connectToWebSocket(String address, String clientId) {
        try {
            String serverUrl = "ws://" + address + "/ws?clientId=" + clientId;
//            WebSocketContainer webSocketContainer = ContainerProvider.getWebSocketContainer();
//            webSocketContainer.setDefaultMaxSessionIdleTimeout(5 * 1000);
//            webSocketContainer.setAsyncSendTimeout(10 * 1000);
            StandardWebSocketClient client = new StandardWebSocketClient(webSocketContainer);
            SessionWrapper sessionWrapper = new SessionWrapper(clientId, address, config.getIp());
            CustomSocketHandler webSocketHandler = new CustomSocketHandler(address, clientId, cacheComponent, objectMapper, eventPublisher, sessionWrapper);
            ListenableFuture<WebSocketSession> webSocketSessionListenableFuture = client.doHandshake(webSocketHandler, new WebSocketHttpHeaders(), URI.create(serverUrl));
            webSocketSessionListenableFuture.addCallback(session -> {
                sessionWrapper.setSession(session);
                sessionWrapper.setOpenTime(LocalDateTime.now());
                SESSIONS.put(address, sessionWrapper);
                log.info("WebSocket Client connected to " + serverUrl + " for client_id: " + clientId);
            }, e -> {
                SESSIONS.put(address, sessionWrapper);
                log.error("WebSocket Client failed to connect to " + serverUrl + " for client_id: " + clientId, e);
            });
        } catch (Exception e) {
            log.error("Error connecting to WebSocket server", e);
            return false;
        }
        return true;
    }

    // 关闭socket连接
    public void closeSession(String hostPort) {
        TimeWheelTool.stopTask(hostPort);
        SessionWrapper sessionWrapper = SESSIONS.get(hostPort);
        if (sessionWrapper != null && sessionWrapper.getSession() != null) {
            try {
                // 移除缓存
                cacheComponent.cache.invalidate(hostPort);
//                Cache<String, PromptVo> ifPresent = cacheComponent.cache.getIfPresent(hostPort);
//                if (ifPresent != null && ifPresent.estimatedSize() == 0) {
//                    // 没有任务了，移除缓存
//                    cacheComponent.cache.invalidate(hostPort);
//                } else {
//                    // 等等任务完成
//                    TimeWheelTool.scheduleTask(hostPort, () -> closeSession(hostPort), 5, TimeUnit.SECONDS);
//                    log.info("hostPort: {} has {} tasks, not remove cache", hostPort, ifPresent.estimatedSize());
//                    return;
////                    throw new RuntimeException("hostPort: " + hostPort + " has " + ifPresent.estimatedSize() + " tasks, not remove cache");
//                }
                // 移除session
                SESSIONS.remove(hostPort);

                sessionWrapper.getSession()
                        .close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        TimeWheelTool.stopTask(hostPort);
    }
}

package com.ai.configui.client.handler;

import cn.hutool.core.util.StrUtil;
import com.ai.configui.client.CacheComponent;
import com.ai.configui.client.SessionWrapper;
import com.ai.configui.client.WebSocketClient;
import com.ai.configui.client.dto.PromptVo;
import com.ai.configui.client.error.ErrorLogManager;
import com.ai.configui.event.PromptEvent;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

import static com.ai.configui.client.WebSocketClient.SESSIONS;
import static com.ai.configui.client.WebSocketClient.initToCache;

@Slf4j
public class CustomSocketHandler extends TextWebSocketHandler {

    private final String hostAndPort;

    private final String clientId;
    private final CacheComponent cacheComponent;
    private final ObjectMapper objectMapper;
    private final ApplicationEventPublisher eventPublisher;
    private final SessionWrapper sessionWrapper;

    public CustomSocketHandler(String hostAndPort, String clientId, CacheComponent cacheComponent, ObjectMapper objectMapper, ApplicationEventPublisher eventPublisher, SessionWrapper sessionWrapper) {
        this.hostAndPort = hostAndPort;
        this.clientId = clientId;
        this.cacheComponent = cacheComponent;
        this.objectMapper = objectMapper;
        this.eventPublisher = eventPublisher;
        this.sessionWrapper = sessionWrapper;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("Connected to server with session id: {} for address:  {} {}", session.getId(), hostAndPort, clientId);
        session.sendMessage(new PingMessage());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        SessionWrapper sessionWrapper = WebSocketClient.SESSIONS.get(hostAndPort);
        if (sessionWrapper != null) {
            sessionWrapper.setCloseTime(LocalDateTime.now());
            sessionWrapper.setSession(null);
            sessionWrapper.setCloseStatus(status);
            WebSocketClient.SESSIONS.put(hostAndPort, sessionWrapper);
        }
        log.info("Connection closed[{}] for session id: {} for address: {} {}, {}", status.getCode(), session.getId(), hostAndPort, clientId, status);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        JsonNode jsonNode = objectMapper.readTree(payload);
        // 检查消息类型
        if (jsonNode.has("type")) {
            String type = jsonNode.get("type")
                    .asText();
            if (!"crystools.monitor".equals(type)) {
                log.debug("get status1:{} {} {}", type, hostAndPort, jsonNode);
                handleStatus(type, jsonNode);
            }
        } else {
            log.info("get status2:{} {}", hostAndPort, jsonNode);
        }

    }

    @Override
    protected void handlePongMessage(WebSocketSession session, PongMessage message) throws Exception {
        // log.debug("Received PONG message from session: {}", session.getRemoteAddress());s
        sessionWrapper.setPingCount(new AtomicInteger(0));
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        try {
            session.close();
        } catch (Exception e) {
            log.error("Error closing WebSocket session: {}", exception.getMessage());
        }
        SessionWrapper sessionWrapper = WebSocketClient.SESSIONS.get(hostAndPort);
        if (sessionWrapper != null) {
            sessionWrapper.setCloseTime(LocalDateTime.now());
            sessionWrapper.setSession(null);
            WebSocketClient.SESSIONS.put(hostAndPort, sessionWrapper);
        }
        log.error("Error handling WebSocket error: ", exception);
    }

    private void handleStatus(String type, JsonNode jsonNode) {
        JsonNode data = jsonNode.get("data");
        if (data == null) {
            log.info("data is null");
            return;
        }
        JsonNode promptNode = data.get("prompt_id");
        String promptId = null;
        if (promptNode == null || promptNode.isNull()) {
//            log.debug("prompt_id is null");
        } else {
            promptId = promptNode.asText();
        }
        switch (type) {
            case "status":
                // 处理 status
//                JsonNode execInfoNode = data.get("exec_info");
//                if (execInfoNode == null) {
//                    log.debug("exec_info is null : ");
//                    break;
//                }
//                JsonNode queueRemainingNode = execInfoNode.path("queue_remaining");
//                if (queueRemainingNode != null && queueRemainingNode.isInt()) {
//                    int queueRemaining = queueRemainingNode.asInt();
//                    sessionWrapper.setQueueSize(queueRemaining);
//                } else {
//                    log.info("queue_remaining is not an integer");
//                }
                break;
            case "execution_start":
                if (StrUtil.isBlank(promptId)) {
                    log.info("execution_start prompt_id is null");
                    break;
                }
                // 处理 execution_start
                JsonNode timestampNode = data.get("timestamp");
                long timestamp = System.currentTimeMillis();
                if (timestampNode != null) {
                    timestamp = timestampNode.asLong();
                }
                SessionWrapper sessionWrapper = SESSIONS.get(hostAndPort);
                if (sessionWrapper != null) {
                    sessionWrapper.incrCount();
                }

                Cache<String, PromptVo> promptVoCache = cacheComponent.cache.getIfPresent(hostAndPort);
                if (promptVoCache == null) {
                    promptVoCache = initToCache(cacheComponent, eventPublisher, hostAndPort);
                }
                PromptVo value = new PromptVo(promptId, timestamp, hostAndPort);
                promptVoCache.put(promptId, value);
                cacheComponent.cache.put(hostAndPort, promptVoCache);
                log.info("promptId execution_start {} : {}", hostAndPort, promptId);
                break;
            case "execution_cached":
                // 处理 execution_cached
                break;
            case "executing":
                // 处理 executing
                JsonNode node1 = data.get("node");
                if (promptId != null && (node1 == null || node1.isNull())) {
                    // 当前执行的promptId 执行完成
                    PromptEvent event = new PromptEvent(this, promptId, hostAndPort, PromptEvent.EventType.executing_completed);
                    eventPublisher.publishEvent(event);
                    log.info("promptId executing end {} : {}", hostAndPort, promptId);
                }
                break;

            case "progress":
                // 处理 progress
                break;
            case "executed":
                // 处理 executed
                break;
            case "execution_success":
                break;
            case "execution_error":
                ErrorLogManager.addErrorAsync(promptId, jsonNode.toString());
                if (StrUtil.isBlank(promptId)) {
                    log.info("execution_error prompt_id is null ");
                    break;
                }
                /**
                 *   data = message['data']
                 error_logger.info(f"prompt_id {prompt_id} execution error, {data}")
                 error_msg = data.get("exception_message", "")
                 */
                String errorMsg = "";
                String exceptionType = "";
                JsonNode exceptionMessage = data.get("exception_message");
                JsonNode exceptionTypeNode = data.get("exception_type");
                if (exceptionMessage != null && !exceptionMessage.isNull()) {
                    errorMsg = exceptionMessage.asText();
                    log.info("promptId execution_error: {} {}", promptId, errorMsg);
                }
                if (exceptionTypeNode != null && !exceptionTypeNode.isNull()) {
                    exceptionType = exceptionTypeNode.asText();
                    log.info("promptId execution_error: {} {}", promptId, exceptionType);
                }
                PromptEvent.EventType eventType = PromptEvent.EventType.execution_error;
                if (StrUtil.isNotBlank(errorMsg)) {
                    if (errorMsg.contains("No face detected"))
                        eventType = PromptEvent.EventType.no_face_detected;
                    else if (exceptionType.contains("OutOfMemoryError")) {
                        eventType = PromptEvent.EventType.out_of_memory_gpu;
                    }
                }

                PromptEvent event = new PromptEvent(this, promptId, hostAndPort, eventType);
                event.setErrorMessage(errorMsg);
                eventPublisher.publishEvent(event);
                log.info("promptId execution_error: {} {}", promptId, jsonNode);
                break;
            case "execution_interrupted":
                if (StrUtil.isBlank(promptId)) {
                    log.info("execution_interrupted prompt_id is null ");
                    break;
                }
                PromptEvent interruptedEvent = new PromptEvent(this, promptId, hostAndPort, PromptEvent.EventType.execution_interrupted);
                eventPublisher.publishEvent(interruptedEvent);
                log.info("promptId execution_interrupted: {} {}", promptId, jsonNode);
                // 处理 download_progress
                break;
            default:
                // 处理未知消息类型
                break;
        }
    }
}
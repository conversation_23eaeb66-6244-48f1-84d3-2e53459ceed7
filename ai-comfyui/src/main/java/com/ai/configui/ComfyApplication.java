package com.ai.configui;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@EnableFeignClients
@EnableScheduling
@ComponentScan(basePackages = {"com.ai"})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class ComfyApplication {

    public static void main(String[] args) {
        SpringApplication.run(ComfyApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  ai启动成功   ლ(´ڡ`ლ)ﾞ");
    }


}

package com.ai.configui.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CustomEnvironmentPostProcessor implements EnvironmentPostProcessor {

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // 在加载 @PropertySource 之前执行的逻辑
        File serverFile = new File("/home/<USER>/ai_logic/config/server.yaml");
        if (!serverFile.exists()) {
            if (!serverFile.getParentFile().exists()) {
                if (serverFile.getParentFile().mkdirs()) {
                    log.info("创建目录成功");
                } else {
                    log.error("创建目录失败");
                    throw new RuntimeException("创建配置文件失败");
                }
            }
            try {
                if (serverFile.createNewFile()) {
                    log.info("创建文件成功");
                } else {
                    log.error("创建文件失败");
                    throw new RuntimeException("创建配置文件失败");
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }
}

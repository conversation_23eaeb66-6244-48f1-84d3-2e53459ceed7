package com.ai.configui.util;

import cn.hutool.core.io.FileUtil;
import com.ai.common.utils.uuid.IdUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class ComfyUtil {

    public static final String CONFIG_FILE_PATH = "/etc/supervisor/conf.d/comfyui.conf";

    public static final String OUTPUT_DIR = "/home/<USER>/comfy_images/%s_output";
    // cache
    private static final Cache<String, String> CACHE = Caffeine.newBuilder()
            .maximumSize(50)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();

    public static void main(String[] args) {
        File file = FileUtil.newFile("/comfy/" + 123 + "/" + IdUtils.simpleUUID() + ".png");
        String queryParamValue = getQueryParamValue("http://localhost:8080/?param=value", "param");
        System.out.println(queryParamValue);
        File imgByPortAndNameAndCopy = findImgByPortAndNameAndCopy("7860", "aiease_flux_00015_.png", file.getAbsolutePath());
        System.out.println(imgByPortAndNameAndCopy.getAbsolutePath());
    }

    public static String getQueryParamValue(String urlString, String paramName) {
        try {
            // 解析 URL
            URL url = new URL(urlString);
            // 获取查询字符串
            String query = url.getQuery();

            // 分割查询参数
            String[] params = query.split("&");
            for (String param : params) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 2 && keyValue[0].equals(paramName)) {
                    // 解码并返回参数值
                    return URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8);
                }
            }
        } catch (Exception e) {
            log.error("getQueryParamValue error", e);
        }
        // 未找到目标参数
        return null;
    }

    public static File findImgByPortAndNameAndCopy(String port, String name, String destinationName) {
        log.info("findImgByPortAndNameAndCopy start, port:{}, name:{}, destinationName:{}", port, name, destinationName);
        try {
            String outputDirectory = findOutputDirectory(CONFIG_FILE_PATH, "comfyui" + port);
            if (outputDirectory == null) {
                outputDirectory = String.format(OUTPUT_DIR, port);
                return null;
            }
            Path firstImage = findFirstImage(outputDirectory, name);
            if (firstImage == null) {
                log.info("findImgByPortAndNameAndCopy error, firstImage is null");
                return null;
            }
            // 转换为 Path 对象
            Path source = firstImage.toAbsolutePath();
            Path destination = Paths.get(destinationName);

            // 确保目标文件夹存在
            Files.createDirectories(destination.getParent());

            // 执行复制操作
            Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING);
            return destination.toFile();
        } catch (Exception e) {
            log.error("findImgByPortAndNameAndCopy error", e);
        }
        return null;
    }

    public static String findOutputDirectory(String supervisorConfigFilePath, String programName) throws IOException {
        if (CACHE.getIfPresent(programName) != null) {
            return CACHE.getIfPresent(programName);
        }
        BufferedReader reader = new BufferedReader(new FileReader(supervisorConfigFilePath));
        String line;
        boolean isTargetProgram = false;
        while ((line = reader.readLine()) != null) {
            line = line.trim();

            // Check for program block
            if (line.startsWith("[program:")) {
                isTargetProgram = line.equals("[program:" + programName + "]");
            }

            // If in target program block, search for output-directory
            if (isTargetProgram && line.contains("--output-directory")) {
                String[] parts = line.split("--output-directory");
                if (parts.length > 1) {
                    String[] args = parts[1].trim().split("\\s+");
                    reader.close();
                    // First part after `--output-directory`
                    String arg = args[0];
                    log.info("find output directory: {}", arg);
                    CACHE.put(programName, arg);
                    return arg;
                }
            }
        }
        reader.close();
        // Not found
        return null;
    }

    public static Path findFirstImage(String directory, String imageName) throws IOException {
        Path dirPath = Paths.get(directory);

        // 使用自定义的 SimpleFileVisitor
        @Getter
        class ImageFileVisitor extends SimpleFileVisitor<Path> {
            private Path result = null;

            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                // 检查文件名是否符合要求
                if (file.getFileName().toString().startsWith(imageName)) {
                    result = file; // 记录匹配的文件
                    return FileVisitResult.TERMINATE; // 停止遍历
                }
                return FileVisitResult.CONTINUE;
            }

        }

        ImageFileVisitor visitor = new ImageFileVisitor();
        Files.walkFileTree(dirPath, visitor);
        // 返回找到的文件路径或 null
        return visitor.getResult();
    }


}

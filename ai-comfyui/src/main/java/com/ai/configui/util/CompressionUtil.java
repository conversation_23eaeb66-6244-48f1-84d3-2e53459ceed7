package com.ai.configui.util;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class CompressionUtil {
    public static byte[] compressWithGzip(String data) throws IOException {
        ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipStream = new GZIPOutputStream(byteStream)) {
            gzipStream.write(data.getBytes("UTF-8"));
        }
        return byteStream.toByteArray();
    }

    public static String compressAndEncodeToBase64(String data) throws IOException {
        byte[] compressedData = compressWithGzip(data);
        return Base64.getEncoder().encodeToString(compressedData);
    }

    public static String decodeBase64AndDecompress(String base64Data) throws IOException {
        // 解码 BASE64 字符串
        byte[] compressedData = Base64.getDecoder().decode(base64Data);

        // 解压缩 GZIP 数据
        try (GZIPInputStream gzipInputStream = new GZIPInputStream(new ByteArrayInputStream(compressedData));
             BufferedReader reader = new BufferedReader(new InputStreamReader(gzipInputStream, StandardCharsets.UTF_8))) {
            StringBuilder result = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line);
            }
            return result.toString();
        }
    }
    public static void main(String[] args) {
        String data = "{\"status\":0,\"message\":\"success\",\"data\":{\"result\":{\"prompt_id\":\"73ffec52-6a60-4fd9-b288-197ce46710c8\",\"prompt_status\":\"success\",\"imgMessageList\":[{\"img_url\":\"/normal/20241104/13/48fcccb3-545a-4174-ade1-61e2dbb20496.png\",\"thumbnail_url\":\"/normal/20241104/13/48fcccb3-545a-4174-ade1-61e2dbb20496.png\",\"high_thumbnail_url\":\"/normal/20241104/13/48fcccb3-545a-4174-ade1-61e2dbb20496.png\",\"sensitive\":null,\"width\":1024,\"height\":1024},{\"img_url\":\"/normal/20241104/13/084b7498-f2ca-4f54-85b1-53c7fe2f8446.png\",\"thumbnail_url\":\"/normal/20241104/13/084b7498-f2ca-4f54-85b1-53c7fe2f8446.png\",\"high_thumbnail_url\":\"/normal/20241104/13/084b7498-f2ca-4f54-85b1-53c7fe2f8446.png\",\"sensitive\":null,\"width\":1024,\"height\":1024},{\"img_url\":\"/normal/20241104/13/b38e4f7e-7de4-4884-959c-6798597f2e9b.png\",\"thumbnail_url\":\"/normal/20241104/13/b38e4f7e-7de4-4884-959c-6798597f2e9b.png\",\"high_thumbnail_url\":\"/normal/20241104/13/b38e4f7e-7de4-4884-959c-6798597f2e9b.png\",\"sensitive\":null,\"width\":1024,\"height\":1024},{\"img_url\":\"/normal/20241104/13/660008e3-fb86-44e2-981b-d59d1785eab9.png\",\"thumbnail_url\":\"/normal/20241104/13/660008e3-fb86-44e2-981b-d59d1785eab9.png\",\"high_thumbnail_url\":\"/normal/20241104/13/660008e3-fb86-44e2-981b-d59d1785eab9.png\",\"sensitive\":null,\"width\":1024,\"height\":1024}]}}}";
        String s = Base64.getEncoder()
                .encodeToString(data.getBytes());
        System.out.println(s.length());
        try {
            System.out.println(data.length());
            String base64Encoded = compressAndEncodeToBase64(data);
            System.out.println("Base64 Encoded GZIP Data: " + base64Encoded.length());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

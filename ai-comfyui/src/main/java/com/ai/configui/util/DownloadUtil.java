package com.ai.configui.util;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import static cn.hutool.core.thread.ThreadUtil.sleep;

/**
 * <AUTHOR>
 */
@Slf4j
public class DownloadUtil {
    private static final int MAX_RETRY = 3;
    private static final Long TRANSFER_TIMEOUT_MILLIS = 20_000L;

    public static boolean downloadImage(String originalUrl, File tempFile) {
        return downloadImage(originalUrl, tempFile, TRANSFER_TIMEOUT_MILLIS, MAX_RETRY);
    }

    public static boolean downloadImage(String imageUrl, String savePath) {
        return downloadImage(imageUrl, new File(savePath), TRANSFER_TIMEOUT_MILLIS, MAX_RETRY);
    }

    /**
     * 下载图片
     *
     * @param imageUrl              图片链接
     * @param saveFile              保存路径
     * @param transferTimeOutMillis 传输超时时间
     * @param maxRetry              最大重试次数
     * @return 是否成功
     */
    public static boolean downloadImage(String imageUrl, File saveFile, Long transferTimeOutMillis, int maxRetry) {
        long start0 = System.currentTimeMillis();
        for (int attempt = 1; attempt <= maxRetry; attempt++) {
            log.info("Attempt {} to download... {}", attempt, imageUrl);
            long start = System.currentTimeMillis();
            HttpURLConnection connection = null;
            InputStream inputStream = null;
            FileOutputStream outputStream = null;
            try {
                URL url = new URL(imageUrl);
                connection = (HttpURLConnection) url.openConnection();
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(10000);
                connection.connect();

                inputStream = connection.getInputStream();

                File parentDir = saveFile.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    parentDir.mkdirs();
                }

                outputStream = new FileOutputStream(saveFile);

                byte[] buffer = new byte[4096];
                int bytesRead;

                long t1 = System.currentTimeMillis();
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    long t2 = System.currentTimeMillis() - t1;
                    if (t2 > transferTimeOutMillis) {
                        log.warn("Transfer timeout, aborting download for [{}], spent time {}s ", imageUrl, (t2) / 1000D);
                        inputStream.close();
                    }
                }
                log.info("Download [{}] completed successfully.spent time {}s ", imageUrl, (System.currentTimeMillis() - start) / 1000D);
                return true;
            } catch (Exception e) {
                log.error("Download failed: {}", e.getMessage(), e);
            } finally {
                try {
                    if (inputStream != null) {
                        inputStream.close();
                    }
                } catch (IOException ignored) {
                    log.warn("关闭输入流失败");
                }
            }
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException ignored) {
                log.warn("关闭输出流失败");
            }
            if (connection != null) {
                connection.disconnect();
            }
            log.warn("Retrying... after 1 second...");
            sleep(1000);
        }
        log.warn("All attempts failed for [{}], spent time {}s ", imageUrl, (System.currentTimeMillis() - start0) / 1000D);
        return false;
    }

}

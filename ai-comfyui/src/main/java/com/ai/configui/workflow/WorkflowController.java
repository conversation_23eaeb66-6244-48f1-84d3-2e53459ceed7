package com.ai.configui.workflow;

import com.ai.common.annotation.Anonymous;
import com.ai.common.core.controller.BaseController;
import com.ai.configui.domain.dto.HistoryVo;
import com.ai.configui.service.ICustomConfigModifyService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import javax.annotation.Resource;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

@RestController
public class WorkflowController extends BaseController {
    String host = "http://*************:7861";
    // String host = "http://*************:8188";
    String websocket = "ws://*************:8188";

    String clientId = "client_piclumen_create";
    @Resource
    RestTemplate restTemplate;
    @Resource
    ICustomConfigModifyService customConfigModifyService;

    @Autowired
    private FreeMarkerConfigurer freeMarkerConfigurer;

    // 定义哪些参数在特定情况下应该被忽略
    private static final Set<String> IGNORED_PARAMS = new HashSet<>();
    ;
    //    private static final Set<String> IGNORED_PARAMS = Set.of("cfg", "samplerName");


    @GetMapping("/test66")
    @Anonymous
    public String test() {
//        customConfigModifyService.addNew("7861");
        return "ok";
    }

    @GetMapping("/test2")
    @Anonymous
    public String test2() {
        customConfigModifyService.remove("7861");
        return "ok";
    }

    @GetMapping("/json")
    @ResponseBody
    @Anonymous
    public String getJson() throws Exception {
        // 创建一个数据模型
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("name", "John Doe");
        dataModel.put("age", 30);
        dataModel.put("city", "New York");

        // 获取 FreeMarker 模板（不带 .ftl 后缀）
        freemarker.template.Template template = freeMarkerConfigurer.getConfiguration()
                .getTemplate("template.json");

        // 将数据模型应用到模板并生成 JSON 字符串
        StringWriter stringWriter = new StringWriter();
        template.process(dataModel, stringWriter);

        return stringWriter.toString();
    }

    @PostMapping("/generateWorkflow")
    @ResponseBody
    @Anonymous
    public String generateWorkflow(@RequestBody Map<String, Object> params) throws IOException, TemplateException {
        // 获取默认值
        Map<String, Object> defaults = DefaultValues.getDefaultValues();

        Map<String, Object> finalParams = new HashMap<>();
        defaults.forEach((key, value) -> {
            //            finalParams.put(key, params.getOrDefault(key, value));
            Object paramValue = params.get(key);
            if (paramValue == null || IGNORED_PARAMS.contains(key)) {
                // 如果参数是null或被标记为无效参数，则使用默认值
                finalParams.put(key, value);
            } else {
                // 否则使用前端传递的值
                finalParams.put(key, paramValue);
            }
        });
        StringWriter stringWriter = new StringWriter();
        Template template = freeMarkerConfigurer.getConfiguration()
                .getTemplate("workflow_template.json");
        template.process(finalParams, stringWriter);

        // 渲染模板并返回JSON
        String jsonOutput = stringWriter.toString();
        return jsonOutput;
    }

    @GetMapping("/getDefaultValues")
    @ResponseBody
    public Map<String, Object> getDefaultValues() {
        // 返回默认值给前端
        return DefaultValues.getDefaultValues();
    }

    @GetMapping("/testImg")
    @ResponseBody
    @Anonymous
    public String testImg() {
        // 返回默认值给前端
        return uploadFileToServer();
    }

    @GetMapping("/testTxt")
    @ResponseBody
    @Anonymous
    public String testTxt() {
        // 返回默认值给前端
        return textToPic();
    }


    @GetMapping("/history/{promptId}")
    @ResponseBody
    @Anonymous
    public List<String> history(@PathVariable("promptId") String promptId) {
        // 返回默认值给前端
        return getHistory(promptId);
    }

    private String textToPic() {
        // prepare the image


        String url = host + "/prompt?client_id=" + clientId;

        // 创建Thymeleaf上下文
        Map<String, Object> context = new HashMap();
        context.put("seed", getSeed(-1));
        context.put("positive", "many beauty girls");
        context.put("negative", "text, watermark");

        StringWriter stringWriter = new StringWriter();

        Template template = null;
        try {
            template = freeMarkerConfigurer.getConfiguration()
                    .getTemplate("texttopic_api.json");
            template.process(context, stringWriter);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 渲染模板并返回JSON
        String prompt = stringWriter.toString();
        String baseJson = makeBaseJson(clientId, prompt);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体对象
        HttpEntity<String> requestEntity = new HttpEntity<>(baseJson, headers);

        // 发送POST请求并获取响应
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        // 获取响应数据
        String responseBody = responseEntity.getBody();

        ObjectMapper om = new ObjectMapper();
        ComfyUIPromptResult comfyUIPromptResult = null;
        try {
            comfyUIPromptResult = om.readValue(responseBody, ComfyUIPromptResult.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        String promptId = comfyUIPromptResult.getPrompt_id();
        return promptId;
    }


    private String makeBaseJson(String clientId, String prompt) {
        // 创建Thymeleaf上下文
        Map<String, Object> context = new HashMap();
        context.put("clientId", clientId);
        context.put("prompt", prompt);

        StringWriter stringWriter = new StringWriter();

        Template template = null;
        try {
            template = freeMarkerConfigurer.getConfiguration()
                    .getTemplate("base.json");
            template.process(context, stringWriter);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 渲染模板并返回JSON
        String jsonOutput = stringWriter.toString();
        return jsonOutput;
    }

    public List<String> getHistory(String promptId) {
        String url = host + "/history/" + promptId;
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        String responseString = response.getBody();
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode jsonNode = mapper.readTree(responseString);
            if (jsonNode.isEmpty()) {
                return new ArrayList<>();
            }
            List<String> fileNames = buildImageUrlsFromJson(jsonNode);
            return fileNames;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public HistoryVo getHistoryAndTimestamp(String promptId) {
        String url = host + "/history/" + promptId;
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        String responseString = response.getBody();
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode jsonNode = mapper.readTree(responseString);
            if (jsonNode.isEmpty()) {
                return null;
            }
            HistoryVo historyVo = new HistoryVo();
            List<String> fileNames = buildImageUrlsFromJson(jsonNode);
            historyVo.setFileNames(fileNames);
            buildStatusTimeMap(jsonNode, historyVo);
            return historyVo;
        } catch (JsonProcessingException e) {
            logger.error("getHistoryAndTimestamp error: ", e);
            return null;
        }
    }

    private void buildStatusTimeMap(JsonNode jsonNode, HistoryVo historyVo) {
        JsonNode firstChild = jsonNode.elements()
                .next();
        JsonNode statusNode = firstChild.get("status");
        if (statusNode == null || statusNode.isNull()) {
            return;
        }
        // Iterator<String> fieldNames = statusNode.fieldNames();
        // while (fieldNames.hasNext()) {
        // String fieldName = fieldNames.next();
        // String statusStr = statusNode.get("status_str")
        //         .asText();
        // boolean completed = statusNode.get("completed")
        //         .asBoolean();

        JsonNode messages = statusNode.path("messages");
        if (messages == null || messages.isNull() || messages.isEmpty()) {
            return;
        }
        for (JsonNode message : messages) {
            String text = message.get(0)
                    .asText();
            if ("execution_start".equals(text)) {
                Long startTimestatmp = message.get(1)
                        .get("timestamp")
                        .asLong();
                historyVo.setStartTimestamp(startTimestatmp);
            } else if ("execution_success".equals(text)) {
                Long endTimestatmp = message.get(1)
                        .get("timestamp")
                        .asLong();
                historyVo.setEndTimestamp(endTimestatmp);
            }
        }
        // }
    }

    public static void main(String[] args) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode jsonNode = objectMapper.readTree(
                    " [\n" + "        [\n" + "          \"execution_start\",\n" + "          {\n" + "            \"prompt_id\": \"bd5b8947-0e59-4444-abee-df26b3ba5046\",\n" + "            \"timestamp\": 1730343830717\n" + "          }\n" + "        ],\n" + "        [\n" + "          \"execution_cached\",\n" + "          {\n" + "            \"nodes\": [],\n" + "            \"prompt_id\": \"bd5b8947-0e59-4444-abee-df26b3ba5046\",\n" + "            \"timestamp\": 1730343830718\n" + "          }\n" + "        ],\n" + "        [\n" + "          \"execution_success\",\n" + "          {\n" + "            \"prompt_id\": \"bd5b8947-0e59-4444-abee-df26b3ba5046\",\n" + "            \"timestamp\": 1730343836149\n" + "          }\n" + "        ]\n" + "      ]");
            String jsonString = jsonNode.toString();
            System.out.println(jsonString);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public List<Image> extractFilename(String json) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode jsonNode = objectMapper.readTree(json);
            JsonNode firstChild = jsonNode.elements()
                    .next();
            JsonNode imagesNode = firstChild.get("outputs")
                    .elements()
                    .next()
                    .get("images");

            List<Image> imageList = new ArrayList<>();

            for (JsonNode imageNode : imagesNode) {
                Image image = objectMapper.treeToValue(imageNode, Image.class);
                imageList.add(image);
            }

            return imageList;
        } catch (JsonProcessingException e) {
            error("解析失败，生成可能还未完成");
        }
        return null;
    }

    public List<String> buildImageUrlsFromJson(JsonNode jsonNode) {
        List<String> imageUrls = new ArrayList<>();
        //        String baseUrl = "http://127.0.0.1:8188/view?filename=";
        String baseUrl = host + "/view?filename=";

        // 使用 Jackson ObjectMapper 解析 JSON 字符串

        try {
            JsonNode firstChild = jsonNode.elements()
                    .next();
            JsonNode outputsNode = firstChild.get("outputs");

            // 遍历 outputs 节点
            Iterator<String> fieldNames = outputsNode.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                JsonNode imagesNode = outputsNode.path(fieldName)
                        .path("images");

                // 遍历 images 数组
                for (JsonNode imageNode : imagesNode) {
                    String filename = imageNode.path("filename")
                            .asText();
                    String type = imageNode.path("type")
                            .asText();

                    // 拼接成完整的URL并添加到列表中
                    String imageUrl = baseUrl + filename + "&type=" + type;
                    imageUrls.add(imageUrl);
                }
            }
            return imageUrls;
        } catch (Exception e) {
            error("解析失败，生成可能还未完成");
            ;
        }
        return null;
    }

    private String uploadFileToServer() {
        // prepare the image

        String host = "http://*************:8188";
        String websocket = "ws://*************:7861";
        String baseUrl = "C:\\Users\\<USER>\\Desktop\\comfyui\\img";
        String filePath1 = "C:\\Users\\<USER>\\Desktop\\comfyui\\img\\" + "1.png";
        String filePath2 = "C:\\Users\\<USER>\\Desktop\\comfyui\\img\\" + "2.png";

        String serverUrl = host + "/upload/image";

        String response1 = uploadFileToServer(filePath1, serverUrl, "");
        String fileName1 = extractUploadFilename(response1);
        String response2 = uploadFileToServer(filePath2, serverUrl, "");
        String fileName2 = extractUploadFilename(response2);

        String url = host + "/prompt?client_id=" + "testName123";

        // 创建Thymeleaf上下文
        Map<String, Object> context = new HashMap();
        context.put("inputImg", fileName1);
        context.put("maskImg", fileName2);

        StringWriter stringWriter = new StringWriter();

        Template template = null;
        try {
            template = freeMarkerConfigurer.getConfiguration()
                    .getTemplate("inpainting_api.json");
            template.process(context, stringWriter);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 渲染模板并返回JSON
        String jsonOutput = stringWriter.toString();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体对象
        HttpEntity<String> requestEntity = new HttpEntity<>(jsonOutput, headers);

        // 发送POST请求并获取响应
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        // 获取响应数据
        String responseBody = responseEntity.getBody();

        ObjectMapper om = new ObjectMapper();
        ComfyUIPromptResult comfyUIPromptResult = null;
        try {
            comfyUIPromptResult = om.readValue(responseBody, ComfyUIPromptResult.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        String promptId = comfyUIPromptResult.getPrompt_id();
        return promptId;
    }

    public ResponseEntity<String> uploadImage(String imagePath, String uploadUrl) throws IOException {
        // 读取图像文件
        Path path = Paths.get(imagePath);
        byte[] imageBytes = Files.readAllBytes(path);
        String filename = path.getFileName()
                .toString();

        // 构建多部分表单数据
        MultipartBodyBuilder builder = new MultipartBodyBuilder();
        builder.part("image", new ByteArrayResource(imageBytes))
                .header("Content-Disposition", "form-data; name=image; filename=" + filename);

        MultiValueMap<String, HttpEntity<?>> multipartRequest = builder.build();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String, HttpEntity<?>>> requestEntity = new HttpEntity<>(multipartRequest, headers);

        // 使用 RestTemplate 发起 POST 请求
        return restTemplate.exchange(uploadUrl, HttpMethod.POST, requestEntity, String.class);
    }

    public static String extractUploadFilename(String json) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode jsonNode = objectMapper.readTree(json);
            return jsonNode.get("name")
                    .asText();
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public String uploadFileToServer(String imageFilePath, String serverUrl, String subfolder) {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("subfolder", subfolder);
        body.add("image", new FileSystemResource(new File(imageFilePath)));

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        return restTemplate.postForObject(serverUrl, requestEntity, String.class);
    }

    public static long getSeed(long seed) {
        if (seed == -1) {
            long min = 100000000000000L; // 最小的 15 位数
            long max = 999999999999999L; // 最大的 15 位数

            // 直接生成一个 [min, max] 范围内的随机数
            return ThreadLocalRandom.current()
                    .nextLong(min, max + 1);
        }
        return seed;
    }
}

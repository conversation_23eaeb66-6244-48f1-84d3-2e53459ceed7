package com.ai.configui.workflow;

import java.util.HashMap;
import java.util.Map;

public class DefaultValues {

    public static Map<String, Object> getDefaultValues() {
        Map<String, Object> defaults = new HashMap<>();
        defaults.put("text", "default text");
        defaults.put("clipId", "19");
        defaults.put("steps", 20);
        defaults.put("cfg", 5.0);
        defaults.put("samplerName", "euler_a");
        defaults.put("scheduler", "default_scheduler");

        return defaults;
    }
}

package com.ai.configui.workflow;

import com.ai.framework.web.service.TokenService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/test")
public class AuthController {

    private final TokenService tokenService;

    public AuthController(TokenService jwtTokenProvider) {
        this.tokenService = jwtTokenProvider;
    }

    @GetMapping("/sign")
    public String sign() {
        // 此处可以是通过用户名和密码的方式进行认证
        // 认证通过后生成 Token
        return "sign ok";
    }
}

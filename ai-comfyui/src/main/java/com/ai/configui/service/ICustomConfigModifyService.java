package com.ai.configui.service;


import com.ai.common.core.domain.AjaxResult;
import com.ai.configui.domain.dto.ComfyServerInfo;
import com.ai.configui.domain.dto.ComfyConfigVo;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */

@Service
public interface ICustomConfigModifyService {

    /**
     * @param paramInfo
     */
    AjaxResult addNew(ComfyServerInfo paramInfo);

    AjaxResult remove(String port);

    /**
     * 查询配置列表
     *
     * @return
     */
    AjaxResult list();

    /**
     * 查询在线session信息
     *
     * @return
     */
    AjaxResult queryOnline();

    /**
     * 查询重连session信息
     *
     * @return
     */
    AjaxResult queryReconnect();

    AjaxResult initConfig(ComfyConfigVo config);

    AjaxResult queryTimeCount(String dateStr);

    AjaxResult queryError(String promptId, String queryDate);
}

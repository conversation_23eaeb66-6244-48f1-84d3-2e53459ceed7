package com.ai.configui.service.impl;

import com.ai.configui.config.CustomConfig;
import com.ai.configui.domain.dto.ComfyServerInfo;
import com.ai.configui.domain.dto.ReadyServerVo;
import com.ai.configui.event.RMqMessage;
import com.ai.configui.service.IReadyServerService;
import com.ai.framework.mq.message.CommonMqMessage;
import com.ai.framework.mq.producer.NormalMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReadyServerServiceImpl implements IReadyServerService {

    @Resource
    private NormalMessageProducer<ReadyServerVo> normalMessageProducer;

    @Resource
    private CustomConfig customConfig;

    @Value("${rocketmq.piclumen.ready.tag:tag_piclumen_ready_test}")
    private String tag;

    @Override
    public void sendReadyStatus(String hostAndPort, String promptId) {
        Map<String, ComfyServerInfo> hostProtServerMap = customConfig.getHostProtServerMap();
        ComfyServerInfo comfyServerInfo = hostProtServerMap.get(hostAndPort);
        if (comfyServerInfo == null) {
            log.warn("未找到对应服务信息，hostAndPort: {}", hostAndPort);
            return;
        }
        ReadyServerVo readyServerVo = new ReadyServerVo(comfyServerInfo.getServerId(), promptId, hostAndPort, true, System.currentTimeMillis());
        CommonMqMessage<ReadyServerVo> commonMqMessage = new RMqMessage<>(comfyServerInfo.getTopic(), tag, readyServerVo.getServerId());
        commonMqMessage.setMessage(readyServerVo);
        normalMessageProducer.syncSend(commonMqMessage);

    }

}

package com.ai.configui.service.impl;

import com.ai.configui.config.MailConfig;
import com.ai.configui.service.EmailService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EmailServiceImpl implements EmailService {


    @Autowired
    private MailConfig mailConfig;
    @Autowired
    private JavaMailSender javaMailSender;


    @Override
    public boolean sendEmail(String to, String subject, String html) {
        Request request = buildRequest(to, subject, html);
        OkHttpClient client = new OkHttpClient().newBuilder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        Call call = client.newCall(request);
        try (Response execute = call.execute()) {
            log.info("response body: {}", Optional.ofNullable(execute.body()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    private Request buildRequest(String to, String subject, String html) {
        RequestBody formBody = new FormBody.Builder().add("apiUser", mailConfig.getApiUser())
                .addEncoded("apiKey", "39411ea65c526a551434263b62f72e4b")
                .addEncoded("from", "<EMAIL>")
                .addEncoded("fromName", "piclumen")
                .addEncoded("to", to)
                .addEncoded("subject", subject)
                .addEncoded("html", html)
                .build();
        Request request = new Request.Builder().url(mailConfig.getSendCloudUrl())
                .addHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
                .addHeader("Accept-Encoding", "gzip, deflate")
                .addHeader("Connection", "keep-alive")
                .addHeader("Accept", "*/*")
                .post(formBody)
                .build();
        return request;
    }


    @Override
    public void sendNotifyMessage(String content, String subject) {
        SimpleMailMessage message = new SimpleMailMessage();
        // 邮件发送人
        message.setFrom(mailConfig.getFrom());
        // 邮件接收人（可以使用 String[] 发送给多个用户）
        message.setTo(mailConfig.getToUsers());
        // 邮件标题
        message.setSubject(subject);
        // 邮件内容
        message.setText(content);
        // 发送邮件
        javaMailSender.send(message);
    }
}

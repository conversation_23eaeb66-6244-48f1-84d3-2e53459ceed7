package com.ai.configui.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.utils.TimeWheelTool;
import com.ai.configui.client.CacheComponent;
import com.ai.configui.client.SessionWrapper;
import com.ai.configui.client.WebSocketClient;
import com.ai.configui.client.error.ErrorLogManager;
import com.ai.configui.client.task.ReconnectTask;
import com.ai.configui.domain.dto.ComfyServerInfo;
import com.ai.configui.config.CustomConfig;
import com.ai.configui.domain.dto.ComfyConfigVo;
import com.ai.configui.service.ICustomConfigModifyService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static com.ai.configui.client.task.ScheduledCountTask.COUNT_PATH;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CustomConfigModifyServiceImpl implements ICustomConfigModifyService {
    @Autowired
    private CustomConfig config;

    @Value("${host.config.path}")
    private String serverConfigPath;

    @Autowired
    private WebSocketClient webSocketClient;
    @Resource
    private CacheComponent cacheComponent;
    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Override
    public AjaxResult addNew(ComfyServerInfo paramInfo) {
        // 新增配置
        if (!config.addNew(paramInfo)) {
            log.info("添加失败, 存在相同的端口 {} {} {}", paramInfo.getPort(), paramInfo.getTopic(), paramInfo.getServerId());
            return AjaxResult.error(String.format("添加失败, 存在相同的端口 %s %s %s", paramInfo.getPort(), paramInfo.getTopic(), paramInfo.getServerId()));
        }
        // 寫配置文件
        String errorMessage = updateToConfigFile();
        if (StrUtil.isNotBlank(errorMessage)) {
            log.info("添加失败, 写入配置文件失败 {} {} {}", paramInfo.getPort(), paramInfo.getTopic(), paramInfo.getServerId());
            return AjaxResult.error(String.format("添加失败, %s %s %s detail: %s", paramInfo.getPort(), paramInfo.getTopic(), paramInfo.getServerId(), errorMessage));
        }
        // 连接socket
        if (!connectSocket(paramInfo.getPort(), paramInfo.getTopic(), paramInfo.getClientId())) {
            log.info("添加成功, 连接socket失败 {} {} {}", paramInfo.getTopic(), paramInfo.getClientId(), paramInfo.getServerId());
            return AjaxResult.error(0, String.format("添加成功, 连接socket失败 %s %s %s", paramInfo.getPort(), paramInfo.getTopic(), paramInfo.getServerId()));
        }
        return AjaxResult.success(true);
    }

    private boolean connectSocket(String port, String topic, String clientId) {
        String address = config.getHost() + ":" + port;
        SessionWrapper sessionWrapper = WebSocketClient.SESSIONS.get(address);
        if (sessionWrapper == null) {
            WebSocketClient.initToCache(cacheComponent, eventPublisher, address);
            if (!webSocketClient.connectToWebSocket(address, clientId)) {
                log.info("socket连接失败 {} {}", port, topic);
                return false;
            }
        }
        return true;
    }


    private String updateToConfigFile() {
        // 创建要写入的对象
        Map<String, Object> props = new HashMap<>();
        Map<String, Object> props2 = new HashMap<>();
        Map<String, Object> props3 = new HashMap<>();
        props.put("custom", props2);
        props2.put("server", props3);
        props3.put("host", config.getHost());
        props3.put("ip", config.getIp());

        // 将 serverAddress 转换为对象列表
        List<ComfyServerInfo> serverAddress = config.getServerAddress();
        List<Map<String, Object>> serverAddressList = new ArrayList<>();
        if (CollUtil.isNotEmpty(serverAddress)) {
            for (ComfyServerInfo address : serverAddress) {
                Map<String, Object> addressMap = new HashMap<>();
                addressMap.put("address", address.getAddress());
                addressMap.put("serverId", address.getServerId());
                addressMap.put("port", address.getPort());
                addressMap.put("topic", address.getTopic());
                addressMap.put("clientId", address.getClientId());
                serverAddressList.add(addressMap);
            }
        }
        props3.put("serverAddress", serverAddressList);
        // 配置 DumperOptions 用于格式化输出
        DumperOptions options = new DumperOptions();
        // 设置缩进
        options.setIndent(2);
        // 设置块样式
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        // 创建 Yaml 实例
        Yaml yaml = new Yaml(options);
        // 将 Java 对象写入 YAML 文件
        try (FileWriter writer = new FileWriter(serverConfigPath)) {
            yaml.dump(props, writer);
        } catch (IOException e) {
            log.error("Error writing YAML file: " + e.getMessage(), e);
            return "Error writing YAML file: " + e.getMessage();
        }
        return null;
    }

    @Override
    public AjaxResult remove(String port) {
        // 关闭socket 连接
        webSocketClient.closeSession(config.getHost() + ":" + port);
        config.remove(port);
        String errorMsg = updateToConfigFile();
        if (StrUtil.isNotBlank(errorMsg)) {
            return AjaxResult.error(errorMsg);
        }
        return AjaxResult.success(true);
    }

    @Override
    public AjaxResult list() {
        ComfyConfigVo comfyConfigVo = new ComfyConfigVo();
        comfyConfigVo.setIp(config.getIp());
        comfyConfigVo.setServerAddress(config.getServerAddress());
        return AjaxResult.success(comfyConfigVo);
    }

    @Override
    public AjaxResult queryOnline() {
        return AjaxResult.success(WebSocketClient.SESSIONS);
    }

    @Override
    public AjaxResult queryReconnect() {
        Map<String, Runnable> taskMap = TimeWheelTool.srcTaskMap;
        Map<String, SessionWrapper> rtn = new HashMap<>();
        taskMap.forEach((k, v) -> {
            if ((v instanceof ReconnectTask)) {
                ReconnectTask reconnectTask = (ReconnectTask) v;
                rtn.put(k, reconnectTask.getSessionWrapper());
            }
        });
        return AjaxResult.success(rtn);
    }

    @Override
    public AjaxResult initConfig(ComfyConfigVo param) {
        List<ComfyServerInfo> topicAddressMap = param.getServerAddress();
//        if (CollUtil.isEmpty(config.getPorts()) || StrUtil.isBlank(config.getClientId()) || StrUtil.isBlank(config.getTopic())) {
//            return AjaxResult.error("参数错误， hostAndPorts, clientId, topic 不能为空");
//        }

        Path filePath = Paths.get(serverConfigPath);
        try {
            if (Files.exists(filePath)) {
                // 读取文件所有内容并去除前后空白
                String content = Files.readString(filePath).trim();
                if (StrUtil.isNotEmpty(content)) {
                    return AjaxResult.error("配置文件已存在，请直接执行上下架逻辑");
                }
            }
        } catch (IOException e) {
            log.error("读取配置文件异常", e);
            return AjaxResult.error("读取配置文件异常: " + e.getMessage());
        }
        AjaxResult andInit = createAndInit(param);
        if (andInit != null && andInit.isError()) {
            return andInit;
        }
        for (ComfyServerInfo serverInfo : topicAddressMap) {

            AjaxResult result = this.addNew(serverInfo);
            if (result.isError()) {
                return result;
            }
        }
        return AjaxResult.success(list());
    }

    @Override
    public AjaxResult queryTimeCount(String dateStr) {
        try {
            // 创建文件夹路径
            String filePath = COUNT_PATH + dateStr + ".json";
            File file = new File(filePath);
            // 自动创建父目录（如果不存在）
            if (!file.exists()) {
                log.warn("目录不存在");
                return AjaxResult.error("文件不存在");
            }

            // 写入 JSON 数据到文件
            try (FileReader reader = new FileReader(file)) {
                BufferedReader bufferedReader = new BufferedReader(reader);

                // 读取文件内容
                StringBuilder jsonContent = new StringBuilder();
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    jsonContent.append(line);
                }
                // 使用 fastjson 解析 JSON 字符串
                JSONObject jsonObject = JSON.parseObject(jsonContent.toString());
                return AjaxResult.success(jsonObject);
            }
        } catch (Exception e) {
            log.error("读取文件失败", e);
        }
        return AjaxResult.error("读取失败");
    }

    @Override
    public AjaxResult queryError(String promptId, String queryDate) {
        String errorMsg = ErrorLogManager.getError(promptId, queryDate);
        return AjaxResult.success(null, errorMsg);
    }

    private AjaxResult createAndInit(ComfyConfigVo config) {
        Map<String, Object> props = new HashMap<>();
        Map<String, Object> props2 = new HashMap<>();
        Map<String, Object> props3 = new HashMap<>();
        props.put("custom", props2);
        props2.put("server", props3);
        props3.put("host", "127.0.0.1");
        this.config.setHost("127.0.0.1");
        this.config.setIp(config.getIp());
        props3.put("ip", config.getIp());
        props3.put("serverAddress", Collections.emptyList());
        // 配置 DumperOptions 用于格式化输出
        DumperOptions options = new DumperOptions();
        // 设置缩进
        options.setIndent(2);
        // 设置块样式
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        // 创建 Yaml 实例
        Yaml yaml = new Yaml(options);
        // 将 Java 对象写入 YAML 文件
        try (FileWriter writer = new FileWriter(serverConfigPath)) {
            yaml.dump(props, writer);
        } catch (IOException e) {
            log.error("写入文件失败", e);
            return AjaxResult.error("IO异常，写入文件失败");
        }
        return null;
    }
}

package com.ai.configui.service.impl;

import com.ai.configui.domain.dto.HistoryVo;
import com.ai.configui.service.IWorkflowService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WorkflowServiceImpl implements IWorkflowService {


    @Resource
    private RestTemplate restTemplate;
    private final String HOST_HISTORY = "http://%s/history/%s";
    private final String HOST_VIEW = "http://%s/view?filename=";

    @Override
    public HistoryVo getHistoryAndTimestamp(String promptId, String hostAndPort) {
        String url = String.format(HOST_HISTORY, hostAndPort, promptId);
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        String responseString = response.getBody();
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode jsonNode = mapper.readTree(responseString);
            if (jsonNode.isEmpty()) {
                return null;
            }
            HistoryVo historyVo = new HistoryVo();
            List<String> fileNames = buildImageUrlsFromJson(jsonNode, hostAndPort);
//            if (fileNames.isEmpty()) {
//                List<String> textList = buildTextList(jsonNode);
//                historyVo.setTextList(textList);
//            }
            historyVo.setFileNames(fileNames);
            buildStatusTimeMap(jsonNode, historyVo);
            return historyVo;
        } catch (JsonProcessingException e) {
            log.error("getHistoryAndTimestamp error: {}", promptId, e);
            return null;

        }
    }

    private List<String> buildTextList(JsonNode jsonNode) {
        List<String> textList = new ArrayList<>();
        // 使用 Jackson ObjectMapper 解析 JSON 字符串
        try {
            JsonNode firstChild = jsonNode.elements()
                    .next();
            JsonNode outputsNode = firstChild.get("outputs");

            // 遍历 outputs 节点
            Iterator<String> fieldNames = outputsNode.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                JsonNode textNode = outputsNode.path(fieldName)
                        .path("text");
                if (textNode == null || textNode.isNull() || textNode.isEmpty()) {
                    continue;
                }

                // 遍历 images 数组
                for (JsonNode text : textNode) {
                    String str = text.asText();
                    textList.add(str);
                }
            }
            return textList;
        } catch (Exception e) {
            log.error("解析失败，生成可能还未完成", e);
        }
        return null;
    }


    private void buildStatusTimeMap(JsonNode jsonNode, HistoryVo historyVo) {
        JsonNode firstChild = jsonNode.elements()
                .next();
        JsonNode statusNode = firstChild.get("status");
        if (statusNode == null || statusNode.isNull()) {
            return;
        }
        // Iterator<String> fieldNames = statusNode.fieldNames();
        // while (fieldNames.hasNext()) {
        // String fieldName = fieldNames.next();
        // String statusStr = statusNode.get("status_str")
        //         .asText();
        // boolean completed = statusNode.get("completed")
        //         .asBoolean();

        JsonNode messages = statusNode.path("messages");
        if (messages == null || messages.isNull() || messages.isEmpty()) {
            return;
        }
        for (JsonNode message : messages) {
            String text = message.get(0)
                    .asText();
            if ("execution_start".equals(text)) {
                Long startTimestatmp = message.get(1)
                        .get("timestamp")
                        .asLong();
                historyVo.setStartTimestamp(startTimestatmp);
            } else if ("execution_success".equals(text)) {
                Long endTimestatmp = message.get(1)
                        .get("timestamp")
                        .asLong();
                historyVo.setEndTimestamp(endTimestatmp);
            }
        }
        // }
    }

    public List<String> buildImageUrlsFromJson(JsonNode jsonNode, String hostAndPort) {
        List<String> imageUrls = new ArrayList<>();
        //        String baseUrl = "http://127.0.0.1:8188/view?filename=";
        String baseUrl = String.format(HOST_VIEW, hostAndPort);

        // 使用 Jackson ObjectMapper 解析 JSON 字符串

        try {
            JsonNode firstChild = jsonNode.elements()
                    .next();
            JsonNode outputsNode = firstChild.get("outputs");

            // 遍历 outputs 节点
            Iterator<String> fieldNames = outputsNode.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                JsonNode imagesNode = outputsNode.path(fieldName)
                        .path("images");
                if (imagesNode == null || imagesNode.isNull() || imagesNode.isEmpty()) {
                    continue;
                }

                // 遍历 images 数组
                for (JsonNode imageNode : imagesNode) {
                    String filename = imageNode.path("filename")
                            .asText();
                    String type = imageNode.path("type")
                            .asText();

                    // 拼接成完整的URL并添加到列表中
                    String imageUrl = baseUrl + filename + "&type=" + type;
                    imageUrls.add(imageUrl);
                }
            }
            return imageUrls;
        } catch (Exception e) {
            log.error("解析失败，生成可能还未完成", e);
        }
        return null;
    }
}

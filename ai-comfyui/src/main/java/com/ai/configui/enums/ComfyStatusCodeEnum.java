package com.ai.configui.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ComfyStatusCodeEnum {
    SUCCESS(2000, "Success"),
    NO_FACE_DETECTED(3001, "No face detected!"),
    TIME_OUT(3002, "Time out"),
    EXECUTION_ERROR(3003, "Execution error"),
    EXECUTION_INTERRUPTED(3004, "Execution interrupted"),
    NOT_FUND_HISTORY(3005, "Not find img from comfy history interface"),
    DEAL_RESULT_ERROR(3006, "Error handle Img or can't find img"),
    OUT_OF_MEMORY_GPU(3007, "Out of memory GPU"),
    UNKNOWN(9999, "Unknown"),
    ;
    private final Integer code;

    private final String desc;

    //如果枚举值中还有数据则必须要创建一个构造函数
    ComfyStatusCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // 根据错误码获取错误信息
    public static String getErrorMessage(int code) {
        for (ComfyStatusCodeEnum errorCode : ComfyStatusCodeEnum.values()) {
            if (errorCode.getCode() == code) {
                return errorCode.getDesc();
            }
        }
        return "Unknown error code: " + code;
    }
}

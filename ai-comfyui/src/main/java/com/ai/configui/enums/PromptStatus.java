package com.ai.configui.enums;


public enum PromptStatus {

    // newbuilt("newbuilt", "新建"),
    success("success", "成功"),
    // running("running", "执行中"),
    // pending("pending", "挂起中"),
    failure("failure", "失败"),

    // cancel("cancel", "取消");
    ;
    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    PromptStatus(String value, String label) {
        this.value = value;
        this.label = label;
    }


    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}

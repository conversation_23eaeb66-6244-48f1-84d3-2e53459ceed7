package com.ai.configui.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class PromptEvent extends ApplicationEvent {

    private String promptId;

    private String hostAndPort;

    private EventType eventType;

    private String errorMessage;

    public PromptEvent(Object source) {
        super(source);
    }

    public PromptEvent(Object src, String promptId, String hostAndPort, EventType eventType) {
        super(src);
        this.promptId = promptId;
        this.hostAndPort = hostAndPort;
        this.eventType = eventType;
    }


    public PromptEvent(Object src, String promptId, String hostAndPort, EventType eventType, String errorMessage) {
        super(src);
        this.promptId = promptId;
        this.hostAndPort = hostAndPort;
        this.eventType = eventType;
        this.errorMessage = errorMessage;
    }


    // eventType 枚举
    public enum EventType {
        /**
         * 执行结果处理出错
         */
        deal_result_error("deal_result_error", "执行结果处理出错"),
        /**
         * 缓存过期
         */
        cache_expired("cache_expired", "缓存过期"),
        /**
         * 执行失败
         */
        execution_error("execution_error", "执行失败"),

        no_face_detected("no_face_detected", "人脸检测失败"),

        out_of_memory_gpu("out_of_memory_gpu", "显存溢出"),
        /**
         * 执行中断
         */
        execution_interrupted("execution_interrupted", "执行中断"),

        not_fund_history("not_fund_history", "comfy history 接口失败"),
        /**
         * 执行完成
         */
        executing_completed("executing_completed", "执行完成");


        @Getter
        private String code;

        @Getter
        private String desc;

        private EventType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        /**
         * 判断是否出错 is error
         */
        public boolean isError() {
            return this.equals(EventType.execution_error) || this.equals(EventType.execution_interrupted) || this.equals(EventType.no_face_detected) || this.equals(EventType.out_of_memory_gpu);
        }

        /**
         * 是socket的事件
         * <p>
         * <p>
         * /
         * 成功
         */

        public boolean isSocketEvent() {
            return this.equals(EventType.execution_error) || this.equals(EventType.execution_interrupted) || this.equals(EventType.executing_completed);
        }

        public boolean isSuccess() {
            return this.equals(EventType.executing_completed);
        }

        /**
         * 缓存过期
         *
         * @return
         */
        public boolean isCacheExpired() {
            return this.equals(EventType.cache_expired);
        }
    }
}

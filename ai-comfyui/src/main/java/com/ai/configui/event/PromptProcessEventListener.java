package com.ai.configui.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.configui.client.CacheComponent;
import com.ai.configui.client.SessionWrapper;
import com.ai.configui.client.dto.PromptVo;
import com.ai.configui.domain.dto.HistoryVo;
import com.ai.configui.service.CreateFileService;
import com.ai.configui.service.IReadyServerService;
import com.ai.configui.service.IWorkflowService;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

import static com.ai.configui.client.WebSocketClient.SESSIONS;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PromptProcessEventListener {
    @Resource
    private CacheComponent cacheComponent;
    @Resource
    private CreateFileService createFileService;
    @Resource
    private IReadyServerService iReadyServerService;

    @Resource
    private IWorkflowService workflowService;

    @EventListener
//    @Async("threadPoolTaskExecutor")
    public void onProcess(PromptEvent event) {
        String promptId = event.getPromptId();
        log.info("promptId:{} 处理event: {}", promptId, event.getEventType());
        if (promptId == null) {
            return;
        }
        String hostAndPort = event.getHostAndPort();
        if (StrUtil.equals(event.getEventType().getCode(), PromptEvent.EventType.executing_completed.getCode())) {
            iReadyServerService.sendReadyStatus(hostAndPort, promptId);
        }
        PromptEvent.EventType eventType = event.getEventType();

        if (eventType.isError()) {
            createFileService.dealFailed(hostAndPort, promptId, eventType, event.getErrorMessage());
            return;
        }

        HistoryVo historyVo = workflowService.getHistoryAndTimestamp(promptId, hostAndPort);
        if (historyVo == null || (CollUtil.isEmpty(historyVo.getFileNames())/* && CollUtil.isEmpty(historyVo.getTextList())*/)) {
            log.info("prompt history: {}  {}图片处理失败", promptId, eventType.getCode());
            createFileService.dealFailed(hostAndPort, promptId, PromptEvent.EventType.not_fund_history, event.getErrorMessage());
            return;
        }

        Long startTimestamp = Optional.ofNullable(historyVo.getStartTimestamp()).orElse(0L);
        Long endTimestamp = Optional.ofNullable(historyVo.getEndTimestamp()).orElse(0L);
        log.info("comfy处理 {} 耗时: {}s ", promptId, (endTimestamp - startTimestamp) / 1000D);
        long t0 = System.currentTimeMillis();
        createFileService.dealSuccess(promptId, hostAndPort, historyVo);
        log.info("处理 prompt: {} 总耗时: {}s, 处理event 耗时: {}s", promptId, (System.currentTimeMillis() - startTimestamp) / 1000D,
                (System.currentTimeMillis() - t0) / 1000D);

        Cache<String, PromptVo> ifPresent = cacheComponent.cache.getIfPresent(hostAndPort);
        if (ifPresent == null) {
            return;
        }
        PromptVo promptVo = ifPresent.getIfPresent(promptId);
        if (promptVo == null) {
            return;
        }
        Long startTimeStamp = promptVo.getStartTimeStamp();
        SessionWrapper sessionWrapper = SESSIONS.get(hostAndPort);
        if (sessionWrapper != null && startTimeStamp != null) {
            sessionWrapper.incrTotalTime(System.currentTimeMillis() - startTimeStamp);
        }
        ifPresent.invalidate(promptId);
    }
}

package com.ai.configui.event;

import com.ai.framework.mq.message.CommonMqMessage;

/**
 * <AUTHOR>
 */

public class RMqMessage<T> extends CommonMqMessage<T> {

    private final String topic;
    private final String tag;

    public RMqMessage(String topic, String tag, String messageKey) {
        this.topic = topic;
        this.tag = tag;
        this.messageKey = messageKey;
    }

    @Override
    public String topic() {
        return topic;
    }

    @Override
    public String tag() {
        return tag;
    }
}

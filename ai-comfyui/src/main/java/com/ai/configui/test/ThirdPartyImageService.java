package com.ai.configui.test;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Service
public class ThirdPartyImageService {
    @Value("${thirdparty.api.url}")
    private String thirdPartyApiUrl;

    @Value("${thirdparty.api.test-mode:false}") // 新增配置项，是否启用测试模式
    private boolean testMode;

    private final RestTemplate restTemplate;

    public ThirdPartyImageService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public String getGeneratedImage(String prompt) throws IOException {
        if (testMode) {
            // 如果处于测试模式，调用本地图片文件读取方法
            return getBase64FromLocalImage();
        } else {
            // 否则调用第三方API
            return callThirdPartyApi(prompt);
        }
    }

    private String callThirdPartyApiGet(String prompt) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(thirdPartyApiUrl)
                .queryParam("prompt", prompt);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                builder.toUriString(),
                HttpMethod.GET,
                entity,
                Map.class
        );

        if (response.getStatusCode().is2xxSuccessful()) {
            Map<String, Object> responseBody = response.getBody();
            assert responseBody != null;
            return (String) responseBody.get("image");
        } else {
            throw new RuntimeException("Failed to generate image from third-party service");
        }
    }

    private String callThirdPartyApi(String prompt) {
        // 创建请求体，包含 prompt 参数
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("prompt", prompt);
        requestBody.put("num_inference_steps",7);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建 HttpEntity，包含请求体和头部信息
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                thirdPartyApiUrl,
                HttpMethod.POST,
                entity,
                Map.class
        );

        if (response.getStatusCode().is2xxSuccessful()) {
            Map<String, Object> responseBody = response.getBody();
            assert responseBody != null;
            return (String) responseBody.get("image_base64");
        } else {
            throw new RuntimeException("Failed to generate image from third-party service");
        }
    }


    private String getBase64FromLocalImage() throws IOException {
//        // 读取 resources 目录下的测试图片
//        String imagePath = "classpath:test-image.jpg";
//        byte[] imageBytes = Files.readAllBytes(Paths.get(imagePath));
//
//        // 将图片字节数组编码为Base64字符串
//        return Base64.getEncoder().encodeToString(imageBytes);
        // 使用 ClassPathResource 来加载类路径下的资源文件
        ClassPathResource resource = new ClassPathResource("test-image.jpg");

        // 获取输入流并读取文件内容
        try (InputStream inputStream = resource.getInputStream()) {
            byte[] imageBytes = inputStream.readAllBytes();

            // 将图片字节数组编码为 Base64 字符串
            return Base64.getEncoder().encodeToString(imageBytes);
        }
    }
}
package com.ai.configui.test;

import com.ai.common.annotation.Anonymous;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Base64;
import java.util.Map;

@RestController
@RequestMapping("/api")
public class ImageGeneratorController {

    private final ThirdPartyImageService imageService;

    public ImageGeneratorController(ThirdPartyImageService imageService) {
        this.imageService = imageService;
    }

    @PostMapping("/generate-image")
    @Anonymous
    public ResponseEntity<byte[]> generateImage(@RequestParam("prompt") String prompt) {
        try {
            // 调用第三方服务生成图片，并返回base64格式的图片数据
            String base64Image = imageService.getGeneratedImage(prompt);

            // 将base64解码为字节数组
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);

            // 设置响应头，指定Content-Type为image/jpeg
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_JPEG);

            // 返回图片字节流
            return ResponseEntity.ok().headers(headers).body(imageBytes);

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body(null);
        }
    }

    @PostMapping("/generate-image2")
    @Anonymous
    public ResponseEntity<String> generateImage2(@RequestBody Map<String, String> request) {
        String prompt = request.get("prompt");
        try {
            String base64Image = imageService.getGeneratedImage(prompt);
            // 拼接 data URL 格式的前缀
            String dataUrl = "data:image/jpeg;base64," + base64Image;
            return ResponseEntity.ok(dataUrl);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error generating image");
        }
    }
}



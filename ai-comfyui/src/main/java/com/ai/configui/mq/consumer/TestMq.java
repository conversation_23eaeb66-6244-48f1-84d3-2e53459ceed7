package com.ai.configui.mq.consumer;


import com.ai.configui.domain.dto.BackendCallBackParams;
import com.ai.configui.domain.dto.ComfyR;
import com.ai.framework.mq.listener.BaseMessageListener;
import com.ai.framework.mq.producer.NormalMessageProducer;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.io.IOException;
import java.util.Base64;

import static com.ai.configui.util.CompressionUtil.decodeBase64AndDecompress;


/**
 * 线程参数根据实际调整
 */
// @Component
//@Component
@Slf4j
//@RocketMQMessageListener(topic = "${rocketmq.piclumen.topic:tp_piclumen_dev}",
//        consumerGroup = "${rocketmq.piclumen.create.group:gid_piclumen_create_dev}",
//        tag = "${rocketmq.piclumen.create.tag:tag_piclumen_create_dev}",
//        consumptionThreadCount = 8)
public class TestMq extends BaseMessageListener {
    @Resource
    private NormalMessageProducer<ComfyR<byte[]>> messageProducer;

    @Override
    public void doWork(MessageView message) {
        ComfyR userInfo = this.getBody(message, ComfyR.class);

        log.info("MessageView: {} ", JSONObject.toJSONString(userInfo));

        // String data = (String) userInfo.getData();
        // try {
        //     String s = decodeBase64AndDecompress(data);
        //     log.info("s: " + s);
        // } catch (IOException e) {
        //     throw new RuntimeException(e);
        // }
    }

    static class UserInfo {
        private String userName;

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }
    }
}

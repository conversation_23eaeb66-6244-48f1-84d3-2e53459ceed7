package com.ai.configui.mq.consumer;


import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import com.ai.configui.api.NdClientApi;
import com.ai.configui.domain.dto.ImageProcessNSFWVo;
import com.ai.configui.event.RMqMessage;
import com.ai.configui.util.DownloadUtil;
import com.ai.framework.mq.listener.BaseMessageListener;
import com.ai.framework.mq.producer.NormalMessageProducer;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.net.URL;


/**
 * @Author: elviswan
 * 线程参数根据实际调整
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.piclumen.nsfw.topic:tp_piclumen_dev}",
        consumerGroup = "${rocketmq.piclumen.nsfw.group:gid_image_nsfw_dev}",
        tag = "${rocketmq.piclumen.nsfw.tag:tag_image_nsfw_result_dev}",
        maxCachedMessageCount = 5,
        consumptionThreadCount = 4)
public class ImageProcessNSFWListener extends BaseMessageListener {
    @Resource
    private NormalMessageProducer<ImageProcessNSFWVo> messageProducer;

    @Value("${rocketmq.piclumen.nsfw.result.topic:tp_piclumen_dev}")
    private String resultTopic;

    @Value("${rocketmq.piclumen.nsfw.result.tag:tag_image_nsfw_result_dev}")
    private String resultTag;

    @Resource
    private NdClientApi ndClientApi;

    @Override
    public void doWork(MessageView message) {
        ImageProcessNSFWVo imageVo = this.getBody(message, ImageProcessNSFWVo.class);
        if (imageVo == null) {
            return;
        }
        log.info("MessageView: {} ", JSONObject.toJSONString(imageVo));
        try {
            process(imageVo);
        } catch (Exception e) {
            imageVo.setNsfwResult(2);
            log.error("nsfw 请求失败: ", e);
        }

        RMqMessage<ImageProcessNSFWVo> commonMqMessage = new RMqMessage<>(resultTopic, resultTag, String.valueOf(imageVo.getFileId()));
        commonMqMessage.setMessage(imageVo);
        messageProducer.syncSend(commonMqMessage);
    }

    private void process(ImageProcessNSFWVo imageVo) {
        // 1. 下载图片到临时目录
        String tempDirPath = "/image_nsfw/" + imageVo.getFileId() + "/";
        log.info("NSFW开始下载图片: {} {}", imageVo.getImageUrl(), imageVo.getFileId());
        try {
            URL url = new URL(imageVo.getImageUrl());
            String filePath = url.getPath();
            String fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
            File tempFile = FileUtil.newFile(tempDirPath + fileName);
            boolean success = DownloadUtil.downloadImage(imageVo.getImageUrl(), tempFile);
            if (success) {
                log.info("NSFW下载图片完成: {} {}", imageVo.getImageUrl(), imageVo.getFileId());
            } else {
                log.error("NSFW下载图片失败: {} {}", imageVo.getImageUrl(), imageVo.getFileId());
                imageVo.setNsfwResult(2);
                return;
            }
            Integer result = ndClientApi.nsfwProcess(tempFile);
            log.info("nsfw处理完成: {}, 结果: {}", imageVo.getImageUrl(), result);
            imageVo.setNsfwResult(result == null ? 2 : result);
        } catch (Exception e) {
            log.error("下载图片失败: ", e);
        } finally {
            FileUtil.del(tempDirPath);
        }
    }
}

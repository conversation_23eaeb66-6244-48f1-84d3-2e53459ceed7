package com.ai.configui.mq.consumer;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.common.utils.uuid.IdUtils;
import com.ai.configui.api.NdClientApi;
import com.ai.configui.domain.dto.FluxResponse;
import com.ai.configui.domain.dto.ImageProcessResultVo;
import com.ai.configui.domain.dto.ImageProcessVo;
import com.ai.configui.domain.dto.TaskPollingVo;
import com.ai.configui.enums.SensitiveMessage;
import com.ai.configui.event.RMqMessage;
import com.ai.configui.util.DownloadUtil;
import com.ai.configui.util.FileUtils;
import com.ai.framework.cos.CosCommonService;
import com.ai.framework.mq.listener.BaseMessageListener;
import com.ai.framework.mq.message.CommonMqMessage;
import com.ai.framework.mq.producer.NormalMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.ai.framework.cos.CosCommonService.buildBatchUploadKeyPrefix;

/**
 * MJ图片处理结果消费者
 *
 * <AUTHOR>
 */
@ConditionalOnProperty(value = "rocketmq.piclumen.process.enabled", havingValue = "true")
@Component
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.piclumen.process.topic:tp_piclumen_test}",
        consumerGroup = "${rocketmq.piclumen.process.group:gid_image_process_logic_test}",
        tag = "${rocketmq.piclumen.process.tag:tag_image_process_test}",
        maxCachedMessageCount = 5,
        consumptionThreadCount = 4)
public class ImageProcessResultListener extends BaseMessageListener {

    private static final Logger logger = LoggerFactory.getLogger("rocketmq-msg");

    @Resource
    private NdClientApi ndClientApi;

    @Resource
    private CosCommonService cosCommonService;

    @Resource
    private NormalMessageProducer<ImageProcessResultVo> normalMessageProducer;

    @Value("${rocketmq.piclumen.process.result.topic:tp_piclumen_test}")
    private String resultTopic;

    @Value("${rocketmq.piclumen.process.result.tag:tag_image_process_result_test}")
    private String resultTag;

    @Autowired
    private NormalMessageProducer<TaskPollingVo> normalMessageProducerPolling;

    @Value("${rocketmq.midjourney.polling.topic:tp_midjourney_polling_test}")
    private String pollingTopic;

    @Value("${rocketmq.midjourney.polling.tag:tag_midjourney_polling_test}")
    private String pollingTag;
    @Autowired
    private RestTemplate restTemplate;

    // 创建一个线程池，用于处理图片
    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 2,
            60, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public void doWork(MessageView message) {
        try {
            // 解析消息
            ImageProcessVo imageProcessVo = this.getBody(message, ImageProcessVo.class);
            logger.info("接收到图片处理消息: jobId={}, loginName={}, markId={}, imageCount={}",
                    imageProcessVo.getJobId(), imageProcessVo.getLoginName(),
                    imageProcessVo.getMarkId(), imageProcessVo.getImageInfos() != null ? imageProcessVo.getImageInfos().size() : 0);

            long t1 = System.currentTimeMillis();
            if (imageProcessVo.getPollingVo() != null) {
                log.info("开始拉取图片结果图片: jobId={}, pollingUrl = {}", imageProcessVo.getPollingVo().getJobId(), imageProcessVo.getPollingVo().getPollingUrl());
                TaskPollingVo pollingVo = imageProcessVo.getPollingVo();
                pollingResult(pollingVo);
                sendPollingMessage(pollingVo, 1);
            } else {
                // 处理图片
                ImageProcessResultVo resultVo = processImages(imageProcessVo);

                log.info("处理图片完成: jobId={}, 耗时: {}s", imageProcessVo.getJobId(), (System.currentTimeMillis() - t1) / 1000D);

                // 发送结果到MQ
                long t2 = System.currentTimeMillis();
                sendResultToMq(resultVo);
                log.info("发送结果到MQ完成: jobId={}, 耗时: {}s", imageProcessVo.getJobId(), (System.currentTimeMillis() - t2) / 1000D);
            }
        } catch (Exception e) {
            log.error("处理图片消息失败", e);
        }
    }

    private void pollingResult(TaskPollingVo pollingVo) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(httpHeaders);
        // 构建带参数的 URL
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(pollingVo.getPollingUrl());
        String finalUrl = uriBuilder.toUriString();
        try {
            // 发送请求
            log.info("开始轮询查询图片结果: jobId={}, pollingUrl = {}", pollingVo.getJobId(), pollingVo.getPollingUrl());
            ResponseEntity<FluxResponse.TaskStatusResponse> exchange = restTemplate.exchange(finalUrl, HttpMethod.GET, requestEntity, FluxResponse.TaskStatusResponse.class);
            pollingVo.setTaskStatusResponse(exchange.getBody());
        } catch (Exception e) {
            log.error("pollingResult error jobId={}", pollingVo.getJobId(), e);
        }
    }

    /**
     * 发送轮询延时消息
     */
    public void sendPollingMessage(TaskPollingVo pollingVo, long delaySeconds) {
        try {
            CommonMqMessage<TaskPollingVo> mqMessage = new RMqMessage<>(
                    pollingTopic,
                    pollingTag,
                    pollingVo.getJobId() + "_" + "ai_logic_rt_" + pollingVo.getCurrentAttempt()
            );
            mqMessage.setMessage(pollingVo);

            normalMessageProducerPolling.syncDelaySend(mqMessage, delaySeconds);

            log.debug("Sent {} polling message for taskId: {}, attempt: {}, delay: {}s",
                    pollingVo.getTaskType(), pollingVo.getJobId(), pollingVo.getCurrentAttempt(), delaySeconds);
        } catch (Exception e) {
            log.error("Failed to send {} polling message for taskId: {}",
                    pollingVo.getTaskType(), pollingVo.getJobId(), e);
        }
    }

    /**
     * 处理图片列表
     */
    private ImageProcessResultVo processImages(ImageProcessVo imageProcessVo) {
        ImageProcessResultVo resultVo = new ImageProcessResultVo();
        resultVo.setJobId(imageProcessVo.getJobId());
        resultVo.setLoginName(imageProcessVo.getLoginName());
        resultVo.setMarkId(imageProcessVo.getMarkId());
        resultVo.setStatus("SUCCESS");
        resultVo.setTaskType(imageProcessVo.getTaskType());

        List<ImageProcessResultVo.ProcessedImageInfo> processedImages = new ArrayList<>();
        List<CompletableFuture<ImageProcessResultVo.ProcessedImageInfo>> futures = new ArrayList<>();

        // 创建批量上传的目录前缀
        String cosPreFix = buildBatchUploadKeyPrefix();
        String cosPath = cosCommonService.getConfig().getCosAccelerateDomain() + cosPreFix;

        // 创建临时目录用于存放所有处理后的图片
        String tempDirPath = "/image_process/" + imageProcessVo.getJobId() + "/";
        File tempDir = new File(tempDirPath);
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }

        log.info("开始处理图片列表: jobId={}", imageProcessVo.getJobId());
        // 并行处理每张图片
        for (ImageProcessVo.ImageInfo imageInfo : imageProcessVo.getImageInfos()) {
            CompletableFuture<ImageProcessResultVo.ProcessedImageInfo> future = CompletableFuture.supplyAsync(() -> {
                return processImage(imageInfo, tempDirPath, cosPath, imageProcessVo.getNsfwCheck() == null || imageProcessVo.getNsfwCheck(), imageProcessVo.getJobId());
            }, EXECUTOR);
            futures.add(future);
        }

        // 等待所有图片处理完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            // 收集处理结果
            for (CompletableFuture<ImageProcessResultVo.ProcessedImageInfo> future : futures) {
                ImageProcessResultVo.ProcessedImageInfo processedImage = future.get();
                if (processedImage != null && "SUCCESS".equals(processedImage.getProcessStatus())) {
                    processedImages.add(processedImage);
                } else {
                    log.error("处理图片失败: 直接失败 jobId={}", imageProcessVo.getJobId());
                    resultVo.setStatus("FAILED");
                    return resultVo;
                }
            }
            // 批量上传到腾讯云
            log.info("开始批量上传图片到腾讯云: jobId={}", imageProcessVo.getJobId());
            cosCommonService.uploadDirectory(cosPreFix, tempDir);
            log.info("批量上传完成: jobId={}", imageProcessVo.getJobId());


        } catch (Exception e) {
            log.error("处理图片列表失败", e);
            resultVo.setStatus("FAILED");
            resultVo.setErrorMessage("处理图片失败: " + e.getMessage());
        } finally {
            // 清理临时目录
            FileUtil.del(tempDir);
        }
        resultVo.setProcessedImages(processedImages);
        return resultVo;
    }

    /**
     * 处理单张图片
     */
    private ImageProcessResultVo.ProcessedImageInfo processImage(ImageProcessVo.ImageInfo imageInfo,
                                                                 String tempDirPath, String cosPath, Boolean nsfwCheck, String jobId) {
        ImageProcessResultVo.ProcessedImageInfo processedImage = new ImageProcessResultVo.ProcessedImageInfo();
        processedImage.setOriginalUrl(imageInfo.getOriginalUrl());
        processedImage.setProcessStatus("SUCCESS");

        String fileName = StrUtil.isNotBlank(imageInfo.getFileName()) ?
                imageInfo.getFileName() : IdUtils.simpleUUID() + ".png";
        int dotIndex = fileName.lastIndexOf('.');
        String baseFileName = dotIndex > 0 ? fileName.substring(0, dotIndex) : fileName;

        File tempFile = null;
//        File originalFile = null;
        File thumbnailFile15 = null;
        File thumbnailFile30 = null;
        File thumbnailFile70 = null;
        File highThumbnailFile90 = null;
        File mosaicFile = null;

        try {
            // 1. 下载图片到临时目录
            log.info("开始下载图片: {} {}", imageInfo.getOriginalUrl(), jobId);
            tempFile = FileUtil.newFile(tempDirPath + baseFileName + ".png");
            long t0 = System.currentTimeMillis();
            boolean success = DownloadUtil.downloadImage(imageInfo.getOriginalUrl(), tempFile);
            if (!success) {
                processedImage.setProcessStatus("FAILED");
                processedImage.setErrorMessage("下载失败");
                return processedImage;
            }
            log.info("下载图片完成 {}: {} spent {}s", jobId, imageInfo.getOriginalUrl(), (System.currentTimeMillis() - t0) / 1000D);

            // 保存原图到临时目录（用于最终上传）
//            originalFile = FileUtil.newFile(tempDirPath + "original_" + baseFileName + ".png");
//            FileUtil.copy(tempFile, originalFile, true);

            // 2. 鉴黄处理
            if (nsfwCheck) {
                log.info("开始鉴黄处理 {}: {}", jobId, imageInfo.getOriginalUrl());
                String ndResult = ndPicture(tempFile);
                log.info("鉴黄处理完成: {}, 结果: {}", imageInfo.getOriginalUrl(), ndResult);

                // 3. 如果有敏感内容，生成马赛克图片
                if (StrUtil.isNotBlank(ndResult)) {
                    mosaicFile = FileUtils.createMosaic(FileUtils.createPathThumbnail30(tempFile, tempDirPath));
                    String signedUrl = cosCommonService.uploadToOss(mosaicFile);
                    processedImage.setSensitive(SensitiveMessage.NSFW.getValue());
                    buildImageSize(mosaicFile, processedImage);
                    processedImage.setSize(mosaicFile.length());

                    processedImage.setImgUrl(signedUrl);
                    processedImage.setThumbnailUrl(signedUrl);
                    processedImage.setHighThumbnailUrl(signedUrl);
                    processedImage.setMiniThumbnailUrl(signedUrl);
                    processedImage.setHighMiniUrl(signedUrl);
                    return processedImage;
                }
            }


            // 4. 正常图片处理 - 生成各种尺寸的缩略图
            log.info("开始生成缩略图: {}", imageInfo.getOriginalUrl());

            // 生成各种规格的缩略图，保存到临时目录
            thumbnailFile15 = FileUtils.createPathThumbnail15(tempFile, tempDirPath);
            thumbnailFile30 = FileUtils.createPathThumbnail30(tempFile, tempDirPath);
            thumbnailFile70 = FileUtils.createPathThumbnail70(tempFile, tempDirPath);
            highThumbnailFile90 = FileUtils.createPathHighThumbnail90(tempFile, tempDirPath);

            // 设置图片尺寸信息（使用高清缩略图的尺寸）
            buildImageSize(highThumbnailFile90, processedImage);
            processedImage.setSize(highThumbnailFile90.length());

            // 5. 设置各种规格图片的URL（批量上传后的URL）
//            processedImage.setImgUrl(cosPath + originalFile.getName()); // 原图URL,现在不需要原图，默认为90%
            processedImage.setImgUrl(cosPath + highThumbnailFile90.getName());
            processedImage.setHighThumbnailUrl(cosPath + highThumbnailFile90.getName());
            processedImage.setThumbnailUrl(cosPath + thumbnailFile70.getName());
            processedImage.setHighMiniUrl(cosPath + thumbnailFile30.getName());
            processedImage.setMiniThumbnailUrl(cosPath + thumbnailFile15.getName());

            log.info("图片处理完成: {} {}", imageInfo.getOriginalUrl(), jobId);

        } catch (Exception e) {
            log.error("处理图片失败: {}", imageInfo.getOriginalUrl(), e);
            processedImage.setProcessStatus("FAILED");
            processedImage.setErrorMessage("处理失败: " + e.getMessage());

            // 清理已生成的文件
            deleteFile(tempFile);
//            deleteFile(originalFile);
            deleteFile(thumbnailFile15);
            deleteFile(thumbnailFile30);
            deleteFile(thumbnailFile70);
            deleteFile(highThumbnailFile90);
            deleteFile(mosaicFile);
        } finally {
            // 清理原图，避免上传
            deleteFile(tempFile);
            deleteFile(mosaicFile);
        }

        return processedImage;
    }

    /**
     * 鉴黄处理
     */
    private String ndPicture(File file) throws IOException {
        return ndClientApi.ndPicture(file);
    }

    /**
     * 设置图片尺寸信息
     */
    private void buildImageSize(File imageFile, ImageProcessResultVo.ProcessedImageInfo processedImage) {
        if (imageFile == null || !imageFile.exists() || !imageFile.canRead()) {
            log.error("文件不存在或无法读取: {}", imageFile != null ? imageFile.getAbsolutePath() : "");
            return;
        }
        try (InputStream inputStream = new FileInputStream(imageFile);
             BufferedInputStream bis = new BufferedInputStream(inputStream)
        ) {
            BufferedImage image = ImageIO.read(bis);
            if (image != null) {
                processedImage.setWidth(image.getWidth());
                processedImage.setHeight(image.getHeight());
            } else {
                log.warn("无法读取文件，可能不是有效的图片格式");
            }
        } catch (IOException e) {
            log.error("获取图片宽高失败", e);
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return ".png";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex);
        }
        return ".png";
    }

    /**
     * 删除文件
     */
    private void deleteFile(File file) {
        if (file != null && file.exists()) {
            try {
                file.delete();
            } catch (Exception e) {
                log.warn("删除临时文件失败: {}", file.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 发送结果到MQ
     */
    private void sendResultToMq(ImageProcessResultVo resultVo) {
        try {
            CommonMqMessage<ImageProcessResultVo> mqMessage = new RMqMessage<>(resultTopic, resultTag, resultVo.getJobId());
            mqMessage.setMessage(resultVo);
            normalMessageProducer.syncSend(mqMessage);
            log.info("发送处理结果到MQ成功: jobId={}, status={}", resultVo.getJobId(), resultVo.getStatus());
        } catch (Exception e) {
            log.error("发送处理结果到MQ失败: jobId={}", resultVo.getJobId(), e);
        }
    }
}

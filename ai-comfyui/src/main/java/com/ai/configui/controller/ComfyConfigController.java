package com.ai.configui.controller;

import com.ai.common.core.domain.AjaxResult;
import com.ai.configui.domain.dto.ComfyServerInfo;
import com.ai.configui.domain.dto.ComfyConfigVo;
import com.ai.configui.service.ICustomConfigModifyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 配置信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/config")
public class ComfyConfigController {

    @Resource
    private ICustomConfigModifyService customConfigModifyService;

    @PostMapping("/add")
    public AjaxResult add(@RequestBody ComfyServerInfo paramInfo) {
        return customConfigModifyService.addNew(paramInfo);
    }

    @PostMapping("/remove")
    public AjaxResult remove(@RequestParam(value = "port") String port) {
        return customConfigModifyService.remove(port);
    }

    @PostMapping("/list")
    public AjaxResult list() {
        return customConfigModifyService.list();
    }

    @PostMapping("/query-online")
    public AjaxResult queryOnline() {
        return customConfigModifyService.queryOnline();
    }

    @PostMapping("/query-reconnect")
    public AjaxResult queryReconnect() {
        return customConfigModifyService.queryReconnect();
    }

    @PostMapping("/init")
    public AjaxResult init(@RequestBody ComfyConfigVo config) {
        return customConfigModifyService.initConfig(config);
    }

    @PostMapping("/query-time-count")
    public AjaxResult queryTimeCount(@RequestParam(value = "dateStr", required = false) String dateStr) {
        if (dateStr == null) {
            dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return customConfigModifyService.queryTimeCount(dateStr);
    }

    @PostMapping("/query-error")
    public AjaxResult queryError(@RequestParam(value = "promptId") String promptId, @RequestParam(value = "queryDate", required = false) String queryDate) {
        return customConfigModifyService.queryError(promptId, queryDate);
    }
}

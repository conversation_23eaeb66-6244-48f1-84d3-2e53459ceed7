package com.ai.configui.domain.dto;

import com.ai.configui.enums.ComfyStatusCodeEnum;

/**
 * 请求响应返回基础类
 *
 * @param <T>
 */
public class ComfyR<T> {

    // 返回状态码 0 成功，1 失败，
    private Integer status;
    //返回消息
    private String message;
    //返回数据
    private T data;

    //Success结果
    public static <T> ComfyR<T> success(T data) {
        ComfyR<T> resultData = new ComfyR<>();
        resultData.setStatus(ComfyStatusCodeEnum.SUCCESS.getCode());
        resultData.setMessage(ComfyStatusCodeEnum.SUCCESS.getDesc());
        resultData.setData(data);
        return resultData;
    }

    public static <T> ComfyR<T> withStatusCode(ComfyStatusCodeEnum errorCode) {
        ComfyR<T> resultData = new ComfyR<>();
        resultData.setStatus(errorCode.getCode());
        resultData.setMessage(errorCode.getDesc());
        return resultData;
    }

    public static <T> ComfyR<T> withStatusCodeMsg(ComfyStatusCodeEnum errorCode, String message) {
        ComfyR<T> resultData = new ComfyR<>();
        resultData.setStatus(errorCode.getCode());
        resultData.setMessage(message);
        return resultData;
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}

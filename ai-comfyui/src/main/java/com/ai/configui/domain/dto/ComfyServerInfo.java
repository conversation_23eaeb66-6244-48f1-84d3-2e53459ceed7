package com.ai.configui.domain.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ComfyServerInfo {
    public ComfyServerInfo() {
    }

    public ComfyServerInfo(String address, String serverId, String port, String topic, String clientId) {
        this.address = address;
        this.serverId = serverId;
        this.port = port;
        this.topic = topic;
        this.clientId = clientId;
    }

    private String address;
    private String serverId;
    private String port;
    private String topic;
    private String clientId;
}
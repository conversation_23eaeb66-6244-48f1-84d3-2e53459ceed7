package com.ai.configui.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ComfyConfigVo implements Serializable {
    private static final long serialVersionUID = 1L;

//    /**
//     * mq 消息主题
//     */
//    private String topic;
//
//    /**
//     * comfy socket clientId
//     */
//    private String clientId;

    /**
     * 本机外网Ip
     */
    private String ip;

//    /**
//     * 服务器id
//     */
//    private String serverId;
//
//
//    private Map<String, String> topicAddressMap;
//
//    /**
//     * eg: [7861]
//     */
//    private List<String> ports;
//
//    /**
//     * key: host+port
//     * value: clientId
//     */
//    private Map<String, String> clientIdMap;

    private List<ComfyServerInfo> serverAddress;

}

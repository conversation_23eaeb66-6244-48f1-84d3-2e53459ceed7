package com.ai.configui.domain.dto;

import lombok.Data;

/**
 * 统一任务状态轮询消息体
 * 支持MJ和Flux两种任务类型
 *
 * <AUTHOR>
 */
@Data
public class TaskPollingVo {

    /**
     * 任务类型：MJ 或 FLUX
     */
    private String taskType;

    /**
     * 任务ID
     */
    private String jobId;

    /**
     * 用户登录名
     */
    private String loginName;

    /**
     * 当前轮询次数
     */
    private Integer currentAttempt;

    /**
     * 最大轮询次数
     */
    private Integer maxAttempts;

    /**
     * 轮询间隔（毫秒）
     */
    private Integer pollingInterval;

    /**
     * 任务创建时间戳
     */
    private Long createTimestamp;

    /**
     * Flux专用：轮询URL
     */
    private String pollingUrl;

    private FluxResponse.TaskStatusResponse taskStatusResponse;

    public TaskPollingVo() {
    }

    public TaskPollingVo(String taskType, String jobId, String loginName, Integer currentAttempt,
                         Integer maxAttempts, Integer pollingInterval, Long createTimestamp, String pollingUrl) {
        this.taskType = taskType;
        this.jobId = jobId;
        this.loginName = loginName;
        this.currentAttempt = currentAttempt;
        this.maxAttempts = maxAttempts;
        this.pollingInterval = pollingInterval;
        this.createTimestamp = createTimestamp;
        this.pollingUrl = pollingUrl;
    }

}
